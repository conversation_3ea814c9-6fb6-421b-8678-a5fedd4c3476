#!/bin/bash

# ================================================================
# RouterOS API 依赖安装脚本
# ================================================================
# 功能: 在Debian 12环境下安装RouterOS API依赖
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境"
    
    # 检查操作系统
    if [[ ! -f /etc/debian_version ]]; then
        log_error "此脚本仅支持Debian系统"
        return 1
    fi
    
    local debian_version
    debian_version=$(cat /etc/debian_version)
    log_info "Debian版本: $debian_version"
    
    # 检查是否为Debian 12
    if [[ "$debian_version" =~ ^12\. ]]; then
        log_info "检测到Debian 12，将使用适当的包管理策略"
        export DEBIAN_12=true
    else
        log_warn "未检测到Debian 12，可能需要调整安装策略"
        export DEBIAN_12=false
    fi
    
    return 0
}

# 检查Python3环境
check_python() {
    log_info "检查Python3环境"
    
    # 检查Python3
    if ! command -v python3 >/dev/null 2>&1; then
        log_error "Python3未安装"
        log_info "安装Python3..."
        sudo apt update
        sudo apt install -y python3
    fi
    
    local python_version
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查Python版本是否支持 (需要3.9+)
    local major minor
    major=$(echo "$python_version" | cut -d. -f1)
    minor=$(echo "$python_version" | cut -d. -f2)
    
    if [[ $major -lt 3 ]] || [[ $major -eq 3 && $minor -lt 9 ]]; then
        log_error "RouterOS API需要Python 3.9+，当前版本: $python_version"
        return 1
    fi
    
    log_success "Python3环境检查通过"
    return 0
}

# 检查pip3环境
check_pip() {
    log_info "检查pip3环境"
    
    # 检查pip3
    if ! command -v pip3 >/dev/null 2>&1; then
        log_info "安装pip3..."
        sudo apt update
        sudo apt install -y python3-pip
    fi
    
    local pip_version
    pip_version=$(pip3 --version 2>&1 | cut -d' ' -f2)
    log_info "pip版本: $pip_version"
    
    log_success "pip3环境检查通过"
    return 0
}

# 安装RouterOS API库
install_routeros_api() {
    log_info "安装RouterOS API库"
    
    # 检查是否已安装
    if python3 -c "import routeros_api; print('RouterOS API已安装，版本:', routeros_api.__version__)" 2>/dev/null; then
        log_success "RouterOS API库已安装"
        return 0
    fi
    
    log_info "安装routeros-api库..."
    
    # Debian 12使用外部管理的Python环境，需要特殊处理
    if [[ "${DEBIAN_12:-false}" == "true" ]]; then
        log_info "Debian 12环境，使用--break-system-packages选项"
        
        # 方式1: 使用--break-system-packages (推荐用于系统脚本)
        if pip3 install --break-system-packages routeros-api==0.21.0; then
            log_success "RouterOS API库安装成功 (系统级)"
        else
            log_warn "系统级安装失败，尝试用户级安装"
            
            # 方式2: 用户级安装
            if pip3 install --user routeros-api==0.21.0; then
                log_success "RouterOS API库安装成功 (用户级)"
                
                # 确保用户级包路径在PATH中
                local user_base
                user_base=$(python3 -m site --user-base)
                local user_bin="$user_base/bin"
                
                if [[ ":$PATH:" != *":$user_bin:"* ]]; then
                    log_info "添加用户级包路径到PATH"
                    echo "export PATH=\"$user_bin:\$PATH\"" >> ~/.bashrc
                    export PATH="$user_bin:$PATH"
                fi
            else
                log_error "RouterOS API库安装失败"
                return 1
            fi
        fi
    else
        # 非Debian 12环境，使用标准安装
        if pip3 install routeros-api==0.21.0; then
            log_success "RouterOS API库安装成功"
        else
            log_error "RouterOS API库安装失败"
            return 1
        fi
    fi
    
    # 验证安装
    if python3 -c "import routeros_api; print('验证成功，版本:', routeros_api.__version__)" 2>/dev/null; then
        log_success "RouterOS API库安装验证成功"
        return 0
    else
        log_error "RouterOS API库安装验证失败"
        return 1
    fi
}

# 安装其他依赖
install_other_deps() {
    log_info "安装其他依赖"
    
    # 安装网络工具 (用于测试连接)
    local packages=("curl" "telnet" "nmap")
    
    for package in "${packages[@]}"; do
        if ! command -v "$package" >/dev/null 2>&1; then
            log_info "安装 $package..."
            sudo apt install -y "$package"
        else
            log_info "$package 已安装"
        fi
    done
    
    log_success "其他依赖安装完成"
}

# 设置脚本权限
setup_script_permissions() {
    log_info "设置脚本权限"
    
    local scripts=(
        "$SCRIPT_DIR/routeros_client.py"
        "$SCRIPT_DIR/routeros_test.py"
        "$SCRIPT_DIR/validate_config.py"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            log_info "设置 $script 可执行权限"
        fi
    done
    
    log_success "脚本权限设置完成"
}

# 创建测试脚本
create_test_script() {
    log_info "创建RouterOS连接测试脚本"
    
    cat > "$SCRIPT_DIR/test_routeros_connection.sh" << 'EOF'
#!/bin/bash

# RouterOS连接测试脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "RouterOS连接测试"
echo "================"

# 检查参数
if [[ $# -lt 3 ]]; then
    echo "用法: $0 <host> <username> <password> [port]"
    echo "示例: $0 *********** admin mypassword 8728"
    exit 1
fi

HOST="$1"
USER="$2"
PASS="$3"
PORT="${4:-8728}"

echo "测试参数:"
echo "  主机: $HOST"
echo "  用户: $USER"
echo "  端口: $PORT"
echo ""

# 测试网络连通性
echo "1. 测试网络连通性..."
if ping -c 1 -W 3 "$HOST" >/dev/null 2>&1; then
    echo "   ✓ 网络连通正常"
else
    echo "   ✗ 网络连通失败"
    exit 1
fi

# 测试API端口
echo "2. 测试API端口..."
if timeout 5 bash -c "</dev/tcp/$HOST/$PORT" 2>/dev/null; then
    echo "   ✓ API端口 $PORT 可访问"
else
    echo "   ✗ API端口 $PORT 不可访问"
    echo "   请检查RouterOS API服务是否启用"
    exit 1
fi

# 测试RouterOS API连接
echo "3. 测试RouterOS API连接..."
if python3 "$SCRIPT_DIR/routeros_client.py" \
    --host "$HOST" \
    --user "$USER" \
    --password "$PASS" \
    --port "$PORT" \
    test; then
    echo "   ✓ RouterOS API连接成功"
else
    echo "   ✗ RouterOS API连接失败"
    exit 1
fi

echo ""
echo "所有测试通过！RouterOS API配置正确。"
EOF

    chmod +x "$SCRIPT_DIR/test_routeros_connection.sh"
    log_success "测试脚本创建完成: $SCRIPT_DIR/test_routeros_connection.sh"
}

# 显示安装总结
show_summary() {
    log_success "RouterOS API依赖安装完成！"
    
    echo ""
    echo "安装总结:"
    echo "=========="
    
    # Python版本
    local python_version
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    echo "Python版本: $python_version"
    
    # RouterOS API版本
    local api_version
    if api_version=$(python3 -c "import routeros_api; print(routeros_api.__version__)" 2>/dev/null); then
        echo "RouterOS API版本: $api_version"
    else
        echo "RouterOS API: 安装失败"
    fi
    
    echo ""
    echo "可用工具:"
    echo "========="
    echo "1. RouterOS客户端: $SCRIPT_DIR/routeros_client.py"
    echo "2. 连接测试脚本: $SCRIPT_DIR/test_routeros_connection.sh"
    echo ""
    echo "使用示例:"
    echo "========="
    echo "# 测试RouterOS连接"
    echo "$SCRIPT_DIR/test_routeros_connection.sh *********** admin password"
    echo ""
    echo "# 获取WAN接口IP"
    echo "python3 $SCRIPT_DIR/routeros_client.py --host *********** --user admin --password password get-wan-ip"
    echo ""
    echo "# 列出所有接口"
    echo "python3 $SCRIPT_DIR/routeros_client.py --host *********** --user admin --password password list-interfaces"
}

# 主函数
main() {
    echo "RouterOS API 依赖安装脚本"
    echo "========================="
    echo ""
    
    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到root权限，建议使用普通用户运行此脚本"
        read -p "是否继续? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "安装已取消"
            exit 0
        fi
    fi
    
    # 执行安装步骤
    check_system || exit 1
    check_python || exit 1
    check_pip || exit 1
    install_routeros_api || exit 1
    install_other_deps || exit 1
    setup_script_permissions || exit 1
    create_test_script || exit 1
    
    echo ""
    show_summary
}

# 执行主函数
main "$@"
