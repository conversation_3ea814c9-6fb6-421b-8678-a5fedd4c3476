#!/usr/bin/env python3
"""
RouterOS配置验证工具
验证RouterOS API配置参数的有效性
"""

import sys
import os
import argparse
import ipaddress
import socket
from typing import Dict, List, Tuple, Optional

# 添加tools目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from routeros_client import RouterOSClient
except ImportError:
    print("错误: 无法导入routeros_client模块", file=sys.stderr)
    print("请确保routeros_client.py在同一目录下", file=sys.stderr)
    sys.exit(1)


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
    
    def add_error(self, message: str):
        """添加错误信息"""
        self.errors.append(message)
        print(f"❌ 错误: {message}")
    
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)
        print(f"⚠️  警告: {message}")
    
    def add_info(self, message: str):
        """添加信息"""
        self.info.append(message)
        print(f"ℹ️  信息: {message}")
    
    def validate_ip_address(self, ip: str, name: str) -> bool:
        """验证IP地址格式"""
        try:
            ipaddress.IPv4Address(ip)
            self.add_info(f"{name} IP地址格式有效: {ip}")
            return True
        except ipaddress.AddressValueError:
            self.add_error(f"{name} IP地址格式无效: {ip}")
            return False
    
    def validate_port(self, port: int, name: str) -> bool:
        """验证端口号"""
        if 1 <= port <= 65535:
            self.add_info(f"{name} 端口号有效: {port}")
            return True
        else:
            self.add_error(f"{name} 端口号无效: {port} (必须在1-65535之间)")
            return False
    
    def validate_network_connectivity(self, host: str, port: int) -> bool:
        """验证网络连通性"""
        try:
            # 测试ICMP ping
            import subprocess
            result = subprocess.run(['ping', '-c', '1', '-W', '3', host], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.add_info(f"网络连通性测试通过: {host}")
            else:
                self.add_warning(f"ICMP ping失败: {host} (可能被防火墙阻止)")
            
            # 测试TCP端口连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                self.add_info(f"TCP端口连接成功: {host}:{port}")
                return True
            else:
                self.add_error(f"TCP端口连接失败: {host}:{port}")
                return False
                
        except Exception as e:
            self.add_error(f"网络连通性测试失败: {e}")
            return False
    
    def validate_routeros_config(self, config: Dict) -> bool:
        """验证RouterOS配置"""
        print("验证RouterOS配置参数...")
        print("=" * 40)
        
        valid = True
        
        # 验证必需参数
        required_params = ['host', 'username', 'password']
        for param in required_params:
            if not config.get(param):
                self.add_error(f"缺少必需参数: {param}")
                valid = False
        
        # 验证IP地址
        if config.get('host'):
            if not self.validate_ip_address(config['host'], 'RouterOS主机'):
                valid = False
        
        # 验证端口
        port = config.get('port', 8728)
        try:
            port = int(port)
            if not self.validate_port(port, 'API'):
                valid = False
        except ValueError:
            self.add_error(f"API端口必须是数字: {port}")
            valid = False
        
        # 验证用户名
        username = config.get('username', '')
        if len(username) < 1:
            self.add_error("用户名不能为空")
            valid = False
        elif len(username) > 64:
            self.add_warning("用户名过长，可能导致连接问题")
        
        # 验证密码
        password = config.get('password', '')
        if len(password) < 1:
            self.add_warning("密码为空，确保RouterOS用户配置正确")
        
        # 验证SSL设置
        use_ssl = config.get('use_ssl', False)
        if isinstance(use_ssl, str):
            use_ssl = use_ssl.lower() in ('true', '1', 'yes', 'on')
        
        if use_ssl:
            self.add_info("启用SSL连接")
            if port == 8728:
                self.add_warning("使用SSL时建议使用端口8729")
        else:
            self.add_info("使用明文连接")
            if port == 8729:
                self.add_warning("端口8729通常用于SSL连接")
        
        # 验证超时设置
        timeout = config.get('timeout', 10)
        try:
            timeout = int(timeout)
            if timeout < 1 or timeout > 300:
                self.add_warning(f"超时时间可能不合适: {timeout}秒 (建议5-30秒)")
        except ValueError:
            self.add_error(f"超时时间必须是数字: {timeout}")
            valid = False
        
        return valid
    
    def test_routeros_connection(self, config: Dict) -> bool:
        """测试RouterOS连接"""
        print("\n测试RouterOS API连接...")
        print("=" * 40)
        
        # 首先测试网络连通性
        host = config.get('host')
        port = int(config.get('port', 8728))
        
        if not self.validate_network_connectivity(host, port):
            return False
        
        # 测试RouterOS API连接
        try:
            client = RouterOSClient(
                host=host,
                username=config.get('username', 'admin'),
                password=config.get('password', ''),
                port=port,
                use_ssl=config.get('use_ssl', False),
                timeout=int(config.get('timeout', 10))
            )
            
            if client.test_connection():
                self.add_info("RouterOS API连接测试成功")
                return True
            else:
                self.add_error("RouterOS API连接测试失败")
                return False
                
        except Exception as e:
            self.add_error(f"RouterOS API连接测试异常: {e}")
            return False
    
    def generate_report(self) -> bool:
        """生成验证报告"""
        print("\n验证报告")
        print("=" * 40)
        
        print(f"错误数量: {len(self.errors)}")
        print(f"警告数量: {len(self.warnings)}")
        print(f"信息数量: {len(self.info)}")
        
        if self.errors:
            print("\n❌ 发现的错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print("\n⚠️  警告信息:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        # 返回是否通过验证 (无错误)
        return len(self.errors) == 0


def load_config_from_env_file(file_path: str) -> Dict:
    """从环境配置文件加载RouterOS配置"""
    config = {}
    
    if not os.path.exists(file_path):
        print(f"配置文件不存在: {file_path}")
        return config
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    
                    # 映射RouterOS相关配置
                    if key == 'ROUTEROS_HOST':
                        config['host'] = value
                    elif key == 'ROUTEROS_USER':
                        config['username'] = value
                    elif key == 'ROUTEROS_PASSWORD':
                        config['password'] = value
                    elif key == 'ROUTEROS_PORT':
                        config['port'] = value
                    elif key == 'ROUTEROS_USE_SSL':
                        config['use_ssl'] = value.lower() in ('true', '1', 'yes', 'on')
                    elif key == 'ROUTEROS_TIMEOUT':
                        config['timeout'] = value
                    elif key == 'ROUTEROS_WAN_INTERFACE':
                        config['wan_interface'] = value
    
    except Exception as e:
        print(f"读取配置文件失败: {e}")
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RouterOS配置验证工具')
    parser.add_argument('--config-file', help='配置文件路径 (deployment.env)')
    parser.add_argument('--host', help='RouterOS设备IP地址')
    parser.add_argument('--user', default='admin', help='用户名')
    parser.add_argument('--password', default='', help='密码')
    parser.add_argument('--port', type=int, default=8728, help='API端口')
    parser.add_argument('--ssl', action='store_true', help='使用SSL')
    parser.add_argument('--timeout', type=int, default=10, help='连接超时')
    parser.add_argument('--no-connection-test', action='store_true', 
                       help='跳过连接测试，仅验证配置')
    
    args = parser.parse_args()
    
    # 加载配置
    config = {}
    
    if args.config_file:
        config = load_config_from_env_file(args.config_file)
        print(f"从配置文件加载: {args.config_file}")
    
    # 命令行参数覆盖配置文件
    if args.host:
        config['host'] = args.host
    if args.user != 'admin':
        config['username'] = args.user
    if args.password:
        config['password'] = args.password
    if args.port != 8728:
        config['port'] = args.port
    if args.ssl:
        config['use_ssl'] = True
    if args.timeout != 10:
        config['timeout'] = args.timeout
    
    # 检查是否有配置
    if not config.get('host'):
        print("错误: 未指定RouterOS主机地址")
        print("请使用 --host 参数或 --config-file 指定配置文件")
        sys.exit(1)
    
    # 创建验证器
    validator = ConfigValidator()
    
    print("RouterOS配置验证工具")
    print("=" * 40)
    print(f"主机: {config.get('host', 'N/A')}")
    print(f"用户: {config.get('username', 'N/A')}")
    print(f"端口: {config.get('port', 'N/A')}")
    print(f"SSL: {config.get('use_ssl', False)}")
    print(f"超时: {config.get('timeout', 'N/A')}秒")
    print()
    
    # 验证配置
    config_valid = validator.validate_routeros_config(config)
    
    # 测试连接 (如果配置有效且未跳过)
    connection_valid = True
    if config_valid and not args.no_connection_test:
        connection_valid = validator.test_routeros_connection(config)
    elif args.no_connection_test:
        print("\n跳过连接测试")
    
    # 生成报告
    overall_valid = validator.generate_report()
    
    # 提供建议
    if not overall_valid:
        print("\n🔧 修复建议:")
        print("1. 检查RouterOS设备IP地址是否正确")
        print("2. 确认RouterOS API服务已启用: /ip service enable api")
        print("3. 验证用户名和密码是否正确")
        print("4. 检查网络连通性和防火墙设置")
        print("5. 确认API端口设置正确 (默认8728)")
    else:
        print("\n✅ 配置验证通过！RouterOS API可以正常使用。")
    
    # 设置退出码
    sys.exit(0 if overall_valid else 1)


if __name__ == "__main__":
    main()
