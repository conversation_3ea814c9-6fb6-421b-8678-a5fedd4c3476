#!/usr/bin/env python3
"""
RouterOS API客户端模块
基于官方routeros-api库实现

依赖: pip3 install routeros-api==0.21.0
文档: https://github.com/socialwifi/RouterOS-api
"""

import sys
import argparse
import logging
import ipaddress
from typing import Optional, Dict, List, Any

try:
    import routeros_api
except ImportError:
    print("错误: 未安装routeros-api库", file=sys.stderr)
    print("请运行: pip3 install routeros-api==0.21.0", file=sys.stderr)
    sys.exit(1)


class RouterOSClient:
    """RouterOS API客户端封装类"""
    
    def __init__(self, host: str, username: str = 'admin', 
                 password: str = '', port: int = 8728, 
                 use_ssl: bool = False, timeout: int = 10):
        """
        初始化RouterOS客户端
        
        Args:
            host: RouterOS设备IP地址
            username: 用户名，默认'admin'
            password: 密码，默认空字符串
            port: API端口，默认8728
            use_ssl: 是否使用SSL，默认False
            timeout: 连接超时时间，默认10秒
        """
        self.host = host
        self.username = username
        self.password = password
        self.port = port
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.connection = None
        self.api = None
        
        # 配置日志
        logging.basicConfig(level=logging.WARNING)
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """
        连接到RouterOS设备
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            # 根据官方文档，RouterOS 6.43+需要使用plaintext_login=True
            self.connection = routeros_api.RouterOsApiPool(
                host=self.host,
                username=self.username,
                password=self.password,
                port=self.port,
                plaintext_login=True,  # RouterOS 6.43+必需
                use_ssl=self.use_ssl,
                ssl_verify=False if self.use_ssl else True,  # 自签名证书
                ssl_verify_hostname=False if self.use_ssl else True
            )
            
            self.api = self.connection.get_api()
            
            # 测试连接 - 获取系统身份
            identity = self.api.get_resource('/system/identity').get()
            if identity:
                self.logger.info(f"成功连接到RouterOS: {identity[0].get('name', 'Unknown')}")
                return True
            else:
                self.logger.error("连接测试失败: 无法获取系统身份")
                return False
                
        except Exception as e:
            self.logger.error(f"连接RouterOS失败: {e}")
            return False
    
    def disconnect(self) -> None:
        """断开连接"""
        if self.connection:
            try:
                self.connection.disconnect()
                self.logger.info("已断开RouterOS连接")
            except Exception as e:
                self.logger.warning(f"断开连接时出错: {e}")
            finally:
                self.connection = None
                self.api = None
    
    def is_public_ip(self, ip: str) -> bool:
        """
        检查IP是否为公网IP
        
        Args:
            ip: IP地址字符串
            
        Returns:
            bool: 是公网IP返回True，否则返回False
        """
        try:
            ip_obj = ipaddress.IPv4Address(ip)
            
            # 检查是否为私有地址
            if ip_obj.is_private:
                return False
            
            # 检查是否为回环地址
            if ip_obj.is_loopback:
                return False
            
            # 检查是否为链路本地地址
            if ip_obj.is_link_local:
                return False
            
            # 检查是否为多播地址
            if ip_obj.is_multicast:
                return False
            
            # 检查是否为保留地址
            if ip_obj.is_reserved:
                return False
            
            return True
            
        except (ipaddress.AddressValueError, ValueError):
            return False
    
    def get_interfaces(self) -> List[Dict[str, Any]]:
        """
        获取所有网络接口
        
        Returns:
            List[Dict]: 接口信息列表
        """
        if not self.api:
            self.logger.error("未连接到RouterOS")
            return []
        
        try:
            interfaces = self.api.get_resource('/interface').get()
            return interfaces
        except Exception as e:
            self.logger.error(f"获取接口列表失败: {e}")
            return []
    
    def get_ip_addresses(self) -> List[Dict[str, Any]]:
        """
        获取所有IP地址
        
        Returns:
            List[Dict]: IP地址信息列表
        """
        if not self.api:
            self.logger.error("未连接到RouterOS")
            return []
        
        try:
            addresses = self.api.get_resource('/ip/address').get()
            return addresses
        except Exception as e:
            self.logger.error(f"获取IP地址列表失败: {e}")
            return []
    
    def detect_wan_interface(self, preferred_interface: Optional[str] = None) -> Optional[str]:
        """
        检测WAN接口
        
        Args:
            preferred_interface: 用户指定的接口名称
            
        Returns:
            Optional[str]: WAN接口名称，未找到返回None
        """
        interfaces = self.get_interfaces()
        if not interfaces:
            return None
        
        # 1. 如果用户指定了接口，优先使用
        if preferred_interface:
            for interface in interfaces:
                if interface.get('name') == preferred_interface:
                    return preferred_interface
        
        # 2. 查找包含'wan'关键字的接口
        for interface in interfaces:
            name = interface.get('name', '').lower()
            if 'wan' in name:
                return interface.get('name')
        
        # 3. 查找第一个以太网接口 (通常是ether1)
        for interface in interfaces:
            name = interface.get('name', '')
            if name.startswith('ether') and interface.get('type') == 'ether':
                return name
        
        # 4. 查找PPPoE接口
        for interface in interfaces:
            name = interface.get('name', '')
            if 'pppoe' in name.lower():
                return name
        
        # 5. 返回第一个启用的接口
        for interface in interfaces:
            if not interface.get('disabled', False):
                return interface.get('name')
        
        return None
    
    def get_wan_ip(self, interface_name: Optional[str] = None) -> Optional[str]:
        """
        获取WAN接口IP地址
        
        Args:
            interface_name: 指定的接口名称，为None时自动检测
            
        Returns:
            Optional[str]: WAN接口的公网IP地址，未找到返回None
        """
        if not self.api:
            self.logger.error("未连接到RouterOS")
            return None
        
        # 检测WAN接口
        wan_interface = self.detect_wan_interface(interface_name)
        if not wan_interface:
            self.logger.error("未找到WAN接口")
            return None
        
        self.logger.info(f"使用WAN接口: {wan_interface}")
        
        # 获取接口的IP地址
        addresses = self.get_ip_addresses()
        
        for addr in addresses:
            if addr.get('interface') == wan_interface:
                address = addr.get('address', '')
                if '/' in address:
                    ip = address.split('/')[0]
                    if self.is_public_ip(ip):
                        self.logger.info(f"找到公网IP: {ip}")
                        return ip
                    else:
                        self.logger.debug(f"跳过私有IP: {ip}")
        
        self.logger.warning(f"接口 {wan_interface} 未找到公网IP")
        return None
    
    def test_connection(self) -> bool:
        """
        测试连接并获取基本信息
        
        Returns:
            bool: 测试成功返回True，失败返回False
        """
        if not self.connect():
            return False
        
        try:
            # 获取系统信息
            identity = self.api.get_resource('/system/identity').get()
            if identity:
                print(f"设备名称: {identity[0].get('name', 'Unknown')}")
            
            # 获取系统资源信息
            resource = self.api.get_resource('/system/resource').get()
            if resource:
                res = resource[0]
                print(f"RouterOS版本: {res.get('version', 'Unknown')}")
                print(f"架构: {res.get('architecture-name', 'Unknown')}")
                print(f"运行时间: {res.get('uptime', 'Unknown')}")
            
            # 测试获取WAN IP
            wan_ip = self.get_wan_ip()
            if wan_ip:
                print(f"WAN接口IP: {wan_ip}")
            else:
                print("未能获取WAN接口IP")
            
            return True
            
        except Exception as e:
            self.logger.error(f"测试连接失败: {e}")
            return False
        finally:
            self.disconnect()


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='RouterOS API客户端')
    parser.add_argument('--host', required=True, help='RouterOS设备IP地址')
    parser.add_argument('--user', default='admin', help='用户名 (默认: admin)')
    parser.add_argument('--password', default='', help='密码')
    parser.add_argument('--port', type=int, default=8728, help='API端口 (默认: 8728)')
    parser.add_argument('--ssl', action='store_true', help='使用SSL连接')
    parser.add_argument('--timeout', type=int, default=10, help='连接超时 (默认: 10秒)')
    parser.add_argument('--interface', help='指定WAN接口名称')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 测试连接命令
    subparsers.add_parser('test', help='测试RouterOS连接')
    
    # 获取WAN IP命令
    subparsers.add_parser('get-wan-ip', help='获取WAN接口IP地址')
    
    # 列出接口命令
    subparsers.add_parser('list-interfaces', help='列出所有网络接口')
    
    # 列出IP地址命令
    subparsers.add_parser('list-addresses', help='列出所有IP地址')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    
    # 创建客户端
    client = RouterOSClient(
        host=args.host,
        username=args.user,
        password=args.password,
        port=args.port,
        use_ssl=args.ssl,
        timeout=args.timeout
    )
    
    try:
        if args.command == 'test':
            success = client.test_connection()
            sys.exit(0 if success else 1)
            
        elif args.command == 'get-wan-ip':
            if client.connect():
                wan_ip = client.get_wan_ip(args.interface)
                if wan_ip:
                    print(wan_ip)
                    sys.exit(0)
                else:
                    print("未能获取WAN接口IP", file=sys.stderr)
                    sys.exit(1)
            else:
                print("连接失败", file=sys.stderr)
                sys.exit(1)
                
        elif args.command == 'list-interfaces':
            if client.connect():
                interfaces = client.get_interfaces()
                for interface in interfaces:
                    print(f"{interface.get('name', 'Unknown')}: {interface.get('type', 'Unknown')} "
                          f"({'启用' if not interface.get('disabled', False) else '禁用'})")
                sys.exit(0)
            else:
                print("连接失败", file=sys.stderr)
                sys.exit(1)
                
        elif args.command == 'list-addresses':
            if client.connect():
                addresses = client.get_ip_addresses()
                for addr in addresses:
                    print(f"{addr.get('interface', 'Unknown')}: {addr.get('address', 'Unknown')}")
                sys.exit(0)
            else:
                print("连接失败", file=sys.stderr)
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n操作被用户中断", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        client.disconnect()


if __name__ == "__main__":
    main()
