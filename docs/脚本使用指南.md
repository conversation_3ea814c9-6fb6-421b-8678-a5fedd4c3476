# Matrix Homeserver 脚本使用指南

## 概述

本文档详细说明Matrix Homeserver项目中各个自动化脚本的功能、使用方法和最佳实践。

## 脚本分类

### 🚀 部署脚本
- **setup.sh**: 初始化部署脚本
- **deploy.sh** (外部): 外部指路牌服务部署脚本

### 🔧 运维脚本
- **ip_watchdog.sh**: IP监控和同步脚本
- **certificate_manager.sh**: 证书管理脚本
- **init_certificate_links.sh**: 证书符号链接管理脚本
- **health_check.sh**: 系统健康检查脚本

### 👨‍💼 管理脚本
- **admin.sh**: Matrix管理员工具脚本

### 💾 维护脚本
- **backup.sh**: 数据备份脚本

## 详细使用说明

### 1. setup.sh - 初始化部署脚本

**功能**: 自动化初始化Matrix Homeserver部署环境

**使用方法**:
```bash
# 完整初始化
./scripts/setup.sh

# 强制重新初始化
./scripts/setup.sh --force

# 跳过依赖检查
./scripts/setup.sh --skip-deps

# 跳过配置文件生成
./scripts/setup.sh --skip-config

# 跳过证书申请
./scripts/setup.sh --skip-certs
```

**执行流程**:
1. 检查运行环境和依赖
2. 创建目录结构
3. 加载和验证配置
4. 生成配置文件
5. 初始化SSL证书
6. 初始化IP同步
7. 启动服务
8. 设置定时任务

### 2. ip_watchdog.sh - IP监控脚本

**功能**: 监控动态公网IP变化，自动更新DNS记录和Coturn配置

**使用方法**:
```bash
# 正常运行 (检查并更新IP)
./scripts/ip_watchdog.sh

# 强制更新IP
./scripts/ip_watchdog.sh --force-update

# 仅检查IP状态
./scripts/ip_watchdog.sh --check-only

# 显示详细输出
./scripts/ip_watchdog.sh --verbose
```

**定时任务**:
```bash
# 每分钟执行
* * * * * /opt/matrix/scripts/ip_watchdog.sh >> /var/log/matrix/ip_watchdog.log 2>&1
```

### 3. certificate_manager.sh - 证书管理脚本

**功能**: 智能管理SSL证书的申请、续期和部署

**使用方法**:
```bash
# 正常运行 (检查并续期证书)
./scripts/certificate_manager.sh

# 强制续期证书
./scripts/certificate_manager.sh --force-renew

# 仅检查证书状态
./scripts/certificate_manager.sh --check-only

# 检查证书到期时间
./scripts/certificate_manager.sh --check-expiry
```

**特性**:
- 智能续期 (14天内到期才触发)
- ECC/RSA证书兼容 (优先ECC，自动兼容RSA)
- 三层符号链接架构 (避免重复申请证书)
- 原子化符号链接更新
- 自动备份证书

**定时任务**:
```bash
# 每天凌晨2点执行
0 2 * * * /opt/matrix/scripts/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1
```

### 4. init_certificate_links.sh - 证书符号链接管理

**功能**: 管理三层证书符号链接结构

**使用方法**:
```bash
# 初始化符号链接
./scripts/init_certificate_links.sh init

# 查看链接状态
./scripts/init_certificate_links.sh status

# 清理无效链接
./scripts/init_certificate_links.sh cleanup
```

**三层证书符号链接架构**:
```
# 源存储层 (acme.sh默认位置，避免重复申请)
/root/.acme.sh/matrix.example.com_ecc/    # ECC证书 (优先)
/root/.acme.sh/matrix.example.com/        # RSA证书 (备选)
  ↓ 符号链接
# 中间层 (部署目录)
/opt/matrix/data/acme/matrix.example.com/
  ↓ 符号链接
# 服务层 (各服务证书目录)
/opt/matrix/data/nginx/certs/ 和 /opt/matrix/data/coturn/certs/
```

### 5. health_check.sh - 系统健康检查

**功能**: 全面检查Matrix Homeserver系统的健康状态

**使用方法**:
```bash
# 基础健康检查
./scripts/health_check.sh

# 详细健康检查
./scripts/health_check.sh --detailed

# 健康检查并自动修复问题
./scripts/health_check.sh --detailed --fix
```

**检查项目**:
- Docker服务状态
- SSL证书状态和符号链接
- 网络连接和DNS解析
- Matrix服务API
- 系统资源使用
- 数据库状态 (详细模式)

**退出码**:
- 0: 健康
- 1: 警告
- 2: 严重问题

**定时任务**:
```bash
# 每5分钟执行
*/5 * * * * /opt/matrix/scripts/health_check.sh >> /var/log/matrix/health_check.log 2>&1
```

### 6. admin.sh - Matrix管理员工具

**功能**: 提供Matrix Homeserver的用户管理、房间管理和服务器管理功能

#### 用户管理
```bash
# 创建用户
./scripts/admin.sh user create alice [password]

# 删除用户
./scripts/admin.sh user delete alice

# 列出所有用户
./scripts/admin.sh user list

# 查看用户信息
./scripts/admin.sh user info alice

# 设置管理员
./scripts/admin.sh user make-admin alice

# 移除管理员权限
./scripts/admin.sh user remove-admin alice

# 重置用户密码
./scripts/admin.sh user reset-password alice
```

#### 房间管理
```bash
# 列出所有房间
./scripts/admin.sh room list

# 查看房间信息
./scripts/admin.sh room info !room_id:example.com

# 查看房间成员
./scripts/admin.sh room members !room_id:example.com
```

#### 服务器管理
```bash
# 查看服务器状态
./scripts/admin.sh server status

# 查看服务器统计
./scripts/admin.sh server stats

# 重启服务器
./scripts/admin.sh server restart
```

#### 数据库管理
```bash
# 数据库清理
./scripts/admin.sh db vacuum

# 数据库备份
./scripts/admin.sh db backup
```

## 脚本依赖关系

### 依赖图
```
setup.sh
├── certificate_manager.sh
│   └── init_certificate_links.sh
├── ip_watchdog.sh
└── health_check.sh
    ├── certificate_manager.sh
    ├── init_certificate_links.sh
    └── ip_watchdog.sh

admin.sh (独立)
```

### 工具函数库
所有脚本共享以下工具函数库：
- **utils/common.sh**: 通用工具函数
- **utils/logging.sh**: 日志记录功能
- **utils/cloudflare_api.sh**: Cloudflare API操作

## 最佳实践

### 1. 脚本执行顺序

**初始部署**:
```bash
1. ./scripts/setup.sh                    # 完整初始化
2. ./scripts/admin.sh user create admin  # 创建管理员
3. ./scripts/health_check.sh --detailed  # 验证部署
```

**日常运维**:
```bash
# 每日检查
./scripts/health_check.sh --detailed

# 每周检查
./scripts/certificate_manager.sh --check-expiry
./scripts/init_certificate_links.sh status
./scripts/admin.sh server stats
```

### 2. 故障排除流程

**服务异常**:
```bash
1. ./scripts/health_check.sh --detailed --fix  # 自动诊断和修复
2. docker compose logs                          # 查看详细日志
3. ./scripts/admin.sh server status            # 检查服务状态
```

**证书问题**:
```bash
1. ./scripts/certificate_manager.sh --check-expiry     # 检查证书状态
2. ./scripts/init_certificate_links.sh status         # 检查符号链接
3. ./scripts/init_certificate_links.sh cleanup        # 清理无效链接
4. ./scripts/init_certificate_links.sh init           # 重新初始化
```

**IP变更问题**:
```bash
1. ./scripts/ip_watchdog.sh --check-only      # 检查IP状态
2. ./scripts/ip_watchdog.sh --force-update    # 强制更新IP
3. ./scripts/health_check.sh                  # 验证修复结果
```

### 3. 监控和告警

**推荐的监控脚本**:
```bash
#!/bin/bash
# 监控脚本示例

# 执行健康检查
if ! /opt/matrix/scripts/health_check.sh --detailed; then
    # 发送告警通知
    echo "Matrix服务器健康检查失败" | mail -s "Matrix Alert" <EMAIL>
fi

# 检查证书有效期
cert_days=$(/opt/matrix/scripts/certificate_manager.sh --check-expiry | grep -o '[0-9]\+')
if [[ $cert_days -lt 7 ]]; then
    echo "SSL证书即将到期: $cert_days 天" | mail -s "Certificate Alert" <EMAIL>
fi
```

### 4. 安全注意事项

1. **脚本权限**: 确保脚本文件权限为 755
2. **日志安全**: 定期轮转和清理日志文件
3. **密码管理**: 避免在命令行中明文传递密码
4. **访问控制**: 限制脚本的执行权限

### 5. 性能优化

1. **并发执行**: 避免同时运行多个资源密集型脚本
2. **日志管理**: 配置适当的日志级别和轮转策略
3. **资源监控**: 定期检查脚本的资源使用情况

## 故障排除

### 常见问题

#### 1. 脚本权限错误
```bash
# 解决方案
chmod +x scripts/*.sh
```

#### 2. 配置文件不存在
```bash
# 解决方案
cp config/deployment.env.template config/deployment.env
nano config/deployment.env
```

#### 3. Docker服务未运行
```bash
# 解决方案
sudo systemctl start docker
docker compose up -d
```

#### 4. 证书符号链接损坏
```bash
# 解决方案
./scripts/init_certificate_links.sh cleanup
./scripts/init_certificate_links.sh init
```

通过合理使用这些脚本，您可以实现Matrix Homeserver的高度自动化管理，大大降低运维工作量并提高系统可靠性。
