# RouterOS API 配置指南

## 概述

本指南详细说明如何配置MikroTik RouterOS的API服务，以便Matrix Homeserver项目能够直接从路由器获取WAN接口的真实公网IP地址。

## 为什么使用RouterOS API

### 优势
1. **准确性**: 直接从路由器获取真实的WAN接口IP，避免NAT或代理影响
2. **可靠性**: 不依赖外部IP查询服务，避免网络限制或服务不可用
3. **实时性**: 路由器IP变化时能立即获取最新地址
4. **安全性**: 内网通信，不暴露查询行为给外部服务

### 适用场景
- 动态IP环境下的Matrix Homeserver部署
- 外部IP查询服务不稳定或被限制的网络环境
- 需要高精度IP监控的生产环境
- 企业内网或受限网络环境

## RouterOS API 配置

### 1. 启用API服务

#### 方法一：通过WebFig界面
1. 打开浏览器，访问路由器管理界面 (通常是 `http://***********`)
2. 登录管理员账户
3. 进入 **IP** → **Services**
4. 找到 **api** 服务
5. 双击编辑，配置如下：
   - **Disabled**: 取消勾选 (启用服务)
   - **Port**: `8728` (默认API端口)
   - **Available From**: `0.0.0.0/0` (允许所有IP访问，或设置为特定IP段)

#### 方法二：通过WinBox
1. 打开WinBox，连接到路由器
2. 进入 **IP** → **Services**
3. 双击 **api** 服务
4. 配置参数同上

#### 方法三：通过SSH/Terminal
```bash
# 连接到RouterOS
ssh admin@***********

# 启用API服务
/ip service enable api

# 设置API端口 (可选，默认8728)
/ip service set api port=8728

# 设置访问限制 (可选)
/ip service set api address=***********/24

# 查看API服务状态
/ip service print where name=api
```

### 2. 创建API用户

为了安全起见，建议创建专门的API用户而不是使用admin账户。

#### 通过WebFig界面
1. 进入 **System** → **Users**
2. 点击 **+** 添加新用户
3. 配置用户信息：
   - **Name**: `matrix-api`
   - **Group**: `full` (完全权限) 或 `read` (只读权限)
   - **Password**: 设置强密码
   - **Disabled**: 取消勾选

#### 通过SSH/Terminal
```bash
# 创建API用户
/user add name=matrix-api group=full password=your_strong_password

# 启用用户
/user set matrix-api disabled=no

# 查看用户列表
/user print
```

### 3. 权限组说明

| 权限组 | 权限范围 | 推荐用途 |
|--------|----------|----------|
| `full` | 完全访问权限 | Matrix部署 (推荐) |
| `read` | 只读权限 | 仅监控IP变化 |
| `write` | 读写权限 | 一般配置修改 |

### 4. 安全配置

#### 限制API访问来源
```bash
# 仅允许特定IP段访问API
/ip service set api address=***********/24

# 仅允许特定IP访问
/ip service set api address=***********00
```

#### 防火墙规则
```bash
# 添加防火墙规则限制API访问
/ip firewall filter add chain=input protocol=tcp dst-port=8728 src-address=***********00 action=accept
/ip firewall filter add chain=input protocol=tcp dst-port=8728 action=drop
```

## Matrix项目集成

### 1. 配置Matrix项目

编辑配置文件 `internal/config/deployment.env`：

```bash
# RouterOS API配置
ROUTEROS_HOST="***********"          # 路由器IP地址
ROUTEROS_PORT="8728"                  # API端口
ROUTEROS_USER="matrix-api"            # API用户名
ROUTEROS_PASSWORD="your_password"     # API密码
ROUTEROS_TIMEOUT="10"                 # 连接超时时间
ROUTEROS_RETRIES="3"                  # 重试次数
```

### 2. 安装RouterOS API依赖

```bash
# 安装RouterOS API依赖
./tools/install_routeros_deps.sh

# 验证安装
python3 -c "import routeros_api; print('RouterOS API版本:', routeros_api.__version__)"
```

### 3. 使用配置工具

```bash
# 验证RouterOS配置
python3 tools/validate_config.py --config-file internal/config/deployment.env

# 测试RouterOS连接
python3 tools/routeros_client.py --host *********** --user admin --password password test

# 获取WAN接口IP
python3 tools/routeros_client.py --host *********** --user admin --password password get-wan-ip

# 列出网络接口
python3 tools/routeros_client.py --host *********** --user admin --password password list-interfaces
```

### 4. 测试IP监控

```bash
# 测试RouterOS连接和配置
python3 tools/validate_config.py --config-file internal/config/deployment.env

# 检查IP获取
./internal/scripts/ip_watchdog.sh --check-only

# 强制更新IP
./internal/scripts/ip_watchdog.sh --force-update
```

## 故障排除

### 常见问题

#### 1. 连接被拒绝
**症状**: `Connection refused` 或 `连接失败`

**解决方案**:
```bash
# 检查API服务状态
/ip service print where name=api

# 确保API服务已启用
/ip service enable api

# 检查端口设置
/ip service set api port=8728
```

#### 2. 认证失败
**症状**: `登录失败` 或 `Authentication failed`

**解决方案**:
```bash
# 检查用户是否存在
/user print where name=matrix-api

# 重置用户密码
/user set matrix-api password=new_password

# 确保用户未被禁用
/user set matrix-api disabled=no
```

#### 3. 权限不足
**症状**: `Access denied` 或 `权限不足`

**解决方案**:
```bash
# 检查用户权限组
/user print where name=matrix-api

# 设置为完全权限
/user set matrix-api group=full
```

#### 4. 网络连接问题
**症状**: `Timeout` 或 `网络不可达`

**解决方案**:
```bash
# 检查网络连通性
ping ***********

# 检查防火墙规则
/ip firewall filter print where dst-port=8728

# 临时禁用防火墙测试
/ip firewall filter disable [find dst-port=8728]
```

#### 5. 无法获取WAN IP
**症状**: `未找到WAN接口` 或 `未找到公网IP`

**解决方案**:
```bash
# 查看所有接口
/interface print

# 查看IP地址分配
/ip address print

# 手动指定WAN接口 (在配置文件中设置)
ROUTEROS_WAN_INTERFACE="WAN"      # 默认WAN接口名称
ROUTEROS_WAN_INTERFACE="ether1"   # 或其他自定义名称
```

**WAN接口检测优先级**:
1. 用户指定的接口名称 (`ROUTEROS_WAN_INTERFACE`)
2. 默认WAN接口名称 (`WAN`)
3. 包含'wan'关键字的接口
4. 第一个以太网接口 (`ether1`)
5. PPPoE接口 (`pppoe-out1`)
6. 第一个启用的接口

### 调试步骤

#### 1. 基础连接测试
```bash
# 测试网络连通性
ping ***********

# 测试API端口
telnet *********** 8728
```

#### 2. API功能测试
```bash
# 使用RouterOS配置脚本测试
./scripts/routeros_setup.sh test --host *********** --user admin --password your_password

# 查看详细错误信息
./scripts/ip_watchdog.sh --test-routeros --verbose
```

#### 3. 日志检查
```bash
# 查看Matrix项目日志
tail -f /var/log/matrix/ip_watchdog.log

# 查看RouterOS系统日志
/log print where topics~"api"
```

## 性能优化

### 1. 连接池配置
```bash
# 调整API连接超时
ROUTEROS_TIMEOUT="5"    # 减少超时时间

# 调整重试次数
ROUTEROS_RETRIES="2"    # 减少重试次数
```

### 2. 缓存策略
```bash
# IP监控脚本已实现智能缓存
# 仅在IP变化时才执行更新操作
```

### 3. 监控频率
```bash
# 调整监控频率 (cron配置)
# 每分钟检查 (默认)
* * * * * /opt/matrix/scripts/ip_watchdog.sh

# 每2分钟检查 (降低频率)
*/2 * * * * /opt/matrix/scripts/ip_watchdog.sh
```

## 安全最佳实践

### 1. 用户管理
- 创建专门的API用户，避免使用admin账户
- 设置强密码，定期更换
- 使用最小权限原则

### 2. 网络安全
- 限制API服务的访问来源IP
- 配置防火墙规则
- 使用VPN或专用网络

### 3. 监控告警
- 监控API访问日志
- 设置异常访问告警
- 定期检查用户权限

### 4. 备份恢复
- 定期备份RouterOS配置
- 记录API用户配置
- 准备应急恢复方案

## 高级配置

### 1. 多WAN环境
```bash
# 在配置文件中指定主WAN接口
ROUTEROS_WAN_INTERFACE="ether1"

# 或者在脚本中实现多WAN检测逻辑
```

### 2. 负载均衡
```bash
# 配置多个RouterOS设备
ROUTEROS_HOST_PRIMARY="***********"
ROUTEROS_HOST_SECONDARY="***********"
```

### 3. 自动故障转移
```bash
# 在ip_watchdog.sh中实现
# RouterOS API失败时自动切换到外部IP服务
```

通过正确配置RouterOS API，Matrix Homeserver项目可以实现更可靠、更准确的动态IP监控，大大提高系统的稳定性和可用性。
