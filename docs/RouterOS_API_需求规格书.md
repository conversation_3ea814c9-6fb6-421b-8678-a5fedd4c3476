# RouterOS API 集成需求规格书

## 1. 项目概述

### 1.1 目标
为Matrix Homeserver项目集成MikroTik RouterOS API功能，实现从路由器直接获取WAN接口IP地址，替代外部IP查询服务，提高IP监控的可靠性和准确性。

### 1.2 背景
- **问题**: 外部IP查询服务可能不稳定、被限制或返回不准确的IP
- **解决方案**: 直接从MikroTik路由器获取真实的WAN接口IP地址
- **优势**: 更可靠、更准确、不依赖外部服务

## 2. 技术规格

### 2.1 RouterOS API官方规范

#### 2.1.1 官方Python库
- **包名**: `routeros-api` (PyPI)
- **版本**: 0.21.0 (2025年3月7日发布)
- **导入方式**: `import routeros_api`
- **维护者**: Social WiFi (jgoclawski, socialwifi)
- **许可证**: MIT License
- **支持Python版本**: 3.9, 3.10, 3.11, 3.12

#### 2.1.2 连接参数规范
```python
routeros_api.RouterOsApiPool(
    host,                    # 必需: RouterOS设备IP地址
    username='admin',        # 可选: 用户名，默认'admin'
    password='',            # 可选: 密码，默认空字符串
    port=8728,              # 可选: API端口，默认8728
    plaintext_login=True,   # 可选: 明文登录(RouterOS 6.43+)，默认False
    use_ssl=False,          # 可选: 使用SSL，默认False
    ssl_verify=True,        # 可选: 验证SSL证书，默认True
    ssl_verify_hostname=True, # 可选: 验证SSL主机名，默认True
    ssl_context=None,       # 可选: 自定义SSL上下文，默认None
)
```

#### 2.1.3 API使用规范
```python
# 连接和认证
connection = routeros_api.RouterOsApiPool('***********', 
                                         username='admin', 
                                         password='password', 
                                         plaintext_login=True)
api = connection.get_api()

# 获取接口列表
interfaces = api.get_resource('/interface').get()

# 获取IP地址
addresses = api.get_resource('/ip/address').get()

# 断开连接
connection.disconnect()
```

### 2.2 Debian 12 环境要求

#### 2.2.1 Python脚本规范
- **Shebang**: 必须使用 `#!/usr/bin/env python3`
- **可执行权限**: 必须设置 `chmod +x script.py`
- **包管理**: 使用系统包管理器或虚拟环境安装依赖

#### 2.2.2 依赖安装方式
```bash
# 方式1: 系统包管理器 (推荐)
sudo apt update
sudo apt install python3-pip
pip3 install --break-system-packages routeros-api

# 方式2: 虚拟环境
python3 -m venv /opt/matrix/venv
source /opt/matrix/venv/bin/activate
pip install routeros-api

# 方式3: 用户安装
pip3 install --user routeros-api
```

### 2.3 项目结构规范

#### 2.3.1 目录结构要求
```
synapse-matrix/
├── internal/                    # 内部部署包 (仅必要文件)
│   ├── docker-compose.yml
│   ├── config/
│   │   └── deployment.env.template
│   └── scripts/
│       ├── setup.sh
│       ├── ip_watchdog.sh
│       └── utils/
│           └── routeros_client.py
├── external/                    # 外部部署包 (仅必要文件)
│   ├── config/
│   └── scripts/
├── tools/                       # 开发和测试工具
│   ├── routeros_setup.py
│   ├── routeros_test.py
│   └── validate_config.py
├── docs/                        # 文档
│   ├── RouterOS_API_需求规格书.md
│   ├── RouterOS配置指南.md
│   └── 部署指南.md
└── tests/                       # 测试文件
    └── test_routeros_api.py
```

#### 2.3.2 文件分类原则
- **internal/external**: 仅包含自动部署必需的文件
- **tools/**: 配置、测试、调试工具
- **docs/**: 文档和指南
- **tests/**: 单元测试和集成测试

## 3. 功能需求

### 3.1 核心功能

#### 3.1.1 IP获取功能
- **输入**: RouterOS连接参数
- **输出**: WAN接口的公网IP地址
- **错误处理**: 连接失败时的降级策略

#### 3.1.2 接口检测逻辑
```python
def detect_wan_interface(api):
    """
    检测WAN接口的优先级顺序:
    1. 用户指定的接口名称
    2. 包含'wan'关键字的接口
    3. 第一个以太网接口 (ether1)
    4. PPPoE接口 (pppoe-out1)
    5. 第一个有公网IP的接口
    """
```

#### 3.1.3 IP验证规则
```python
def is_public_ip(ip):
    """
    验证是否为公网IP:
    - 排除私有地址段: 192.168.x.x, 10.x.x.x, 172.16-31.x.x
    - 排除回环地址: 127.x.x.x
    - 排除链路本地地址: 169.254.x.x
    - 验证IP格式有效性
    """
```

### 3.2 集成要求

#### 3.2.1 与ip_watchdog.sh集成
- **优先级**: RouterOS API > 外部IP服务
- **降级策略**: API失败时自动切换到外部服务
- **配置检测**: 自动检测是否配置了RouterOS参数

#### 3.2.2 配置管理
```bash
# deployment.env中的RouterOS配置
ROUTEROS_HOST=""                 # RouterOS设备IP
ROUTEROS_PORT="8728"            # API端口
ROUTEROS_USER=""                # 用户名
ROUTEROS_PASSWORD=""            # 密码
ROUTEROS_USE_SSL="false"        # 是否使用SSL
ROUTEROS_TIMEOUT="10"           # 连接超时
ROUTEROS_WAN_INTERFACE=""       # WAN接口名 (可选)
```

### 3.3 错误处理

#### 3.3.1 连接错误
- **网络不可达**: 记录错误，切换到外部服务
- **认证失败**: 记录错误，提示检查用户名密码
- **API服务未启用**: 记录错误，提示启用API服务

#### 3.3.2 数据错误
- **无WAN接口**: 记录警告，尝试其他检测方法
- **无公网IP**: 记录警告，可能是内网环境
- **IP格式错误**: 记录错误，验证数据完整性

## 4. 实现规范

### 4.1 代码结构

#### 4.1.1 RouterOS客户端模块
```python
#!/usr/bin/env python3
"""
RouterOS API客户端模块
基于官方routeros-api库实现
"""

import routeros_api
import logging
import sys
from typing import Optional, Dict, List

class RouterOSClient:
    """RouterOS API客户端封装类"""
    
    def __init__(self, host: str, username: str = 'admin', 
                 password: str = '', port: int = 8728, 
                 use_ssl: bool = False, timeout: int = 10):
        """初始化RouterOS客户端"""
        
    def connect(self) -> bool:
        """连接到RouterOS设备"""
        
    def disconnect(self) -> None:
        """断开连接"""
        
    def get_wan_ip(self, interface_name: Optional[str] = None) -> Optional[str]:
        """获取WAN接口IP地址"""
        
    def get_interfaces(self) -> List[Dict]:
        """获取所有网络接口"""
        
    def get_ip_addresses(self) -> List[Dict]:
        """获取所有IP地址"""
```

#### 4.1.2 Shell脚本集成
```bash
#!/bin/bash
# ip_watchdog.sh中的RouterOS集成

get_current_ip() {
    local ip=""
    
    # 检查RouterOS配置
    if [[ -n "${ROUTEROS_HOST:-}" ]] && [[ -n "${ROUTEROS_USER:-}" ]]; then
        # 调用Python RouterOS客户端
        if ip=$(python3 "${SCRIPT_DIR}/utils/routeros_client.py" \
                --host "$ROUTEROS_HOST" \
                --user "$ROUTEROS_USER" \
                --password "$ROUTEROS_PASSWORD" \
                --port "${ROUTEROS_PORT:-8728}" \
                --timeout "${ROUTEROS_TIMEOUT:-10}" \
                get-wan-ip 2>/dev/null); then
            
            if validate_ip "$ip"; then
                echo "$ip"
                return 0
            fi
        fi
    fi
    
    # 降级到外部IP服务
    get_external_ip
}
```

### 4.2 安装和部署

#### 4.2.1 依赖安装脚本
```bash
#!/bin/bash
# install_routeros_deps.sh

install_routeros_dependencies() {
    log_info "安装RouterOS API依赖"
    
    # 检查Python3
    if ! command -v python3 >/dev/null 2>&1; then
        log_error "Python3未安装"
        return 1
    fi
    
    # 检查pip3
    if ! command -v pip3 >/dev/null 2>&1; then
        log_info "安装pip3"
        sudo apt update
        sudo apt install -y python3-pip
    fi
    
    # 安装routeros-api
    if ! python3 -c "import routeros_api" 2>/dev/null; then
        log_info "安装routeros-api库"
        pip3 install --break-system-packages routeros-api==0.21.0
    fi
    
    log_info "RouterOS API依赖安装完成"
}
```

#### 4.2.2 配置验证脚本
```python
#!/usr/bin/env python3
"""
RouterOS配置验证工具
"""

def validate_routeros_config(config: Dict) -> bool:
    """验证RouterOS配置参数"""
    
def test_routeros_connection(config: Dict) -> bool:
    """测试RouterOS连接"""
    
def main():
    """主函数"""
    
if __name__ == "__main__":
    main()
```

## 5. 测试要求

### 5.1 单元测试

#### 5.1.1 RouterOS客户端测试
```python
import unittest
from unittest.mock import Mock, patch
from tools.routeros_client import RouterOSClient

class TestRouterOSClient(unittest.TestCase):
    
    def test_connection(self):
        """测试连接功能"""
        
    def test_get_wan_ip(self):
        """测试获取WAN IP功能"""
        
    def test_error_handling(self):
        """测试错误处理"""
```

### 5.2 集成测试

#### 5.2.1 端到端测试
- **RouterOS设备连接测试**
- **IP获取功能测试**
- **降级策略测试**
- **配置验证测试**

## 6. 部署指南

### 6.1 RouterOS设备配置

#### 6.1.1 启用API服务
```bash
# 通过SSH连接RouterOS
/ip service enable api
/ip service set api port=8728
```

#### 6.1.2 创建API用户
```bash
# 创建专用API用户
/user add name=matrix-api group=full password=strong_password
/user set matrix-api disabled=no
```

### 6.2 Matrix项目配置

#### 6.2.1 配置文件设置
```bash
# 编辑deployment.env
ROUTEROS_HOST="***********"
ROUTEROS_USER="matrix-api"
ROUTEROS_PASSWORD="strong_password"
```

#### 6.2.2 测试和验证
```bash
# 测试RouterOS连接
python3 tools/routeros_test.py

# 验证IP获取
./scripts/ip_watchdog.sh --check-only
```

## 7. 维护和监控

### 7.1 日志记录
- **连接状态日志**
- **IP获取成功/失败日志**
- **错误详情和堆栈跟踪**

### 7.2 监控指标
- **RouterOS API可用性**
- **IP获取成功率**
- **响应时间统计**

### 7.3 告警机制
- **连续失败告警**
- **配置错误告警**
- **性能异常告警**

## 8. 安全考虑

### 8.1 认证安全
- **使用专用API用户**
- **强密码策略**
- **定期密码轮换**

### 8.2 网络安全
- **限制API访问来源**
- **使用SSL加密 (推荐)**
- **防火墙规则配置**

### 8.3 数据安全
- **敏感信息加密存储**
- **日志脱敏处理**
- **访问权限控制**

## 9. 性能要求

### 9.1 响应时间
- **连接建立**: < 5秒
- **IP获取**: < 3秒
- **总体响应**: < 10秒

### 9.2 资源使用
- **内存占用**: < 50MB
- **CPU使用**: < 5%
- **网络带宽**: < 1KB/s

### 9.3 可靠性
- **连接成功率**: > 99%
- **IP获取准确性**: 100%
- **降级策略有效性**: > 95%

## 10. 文档要求

### 10.1 用户文档
- **RouterOS配置指南**
- **Matrix项目集成指南**
- **故障排除手册**

### 10.2 开发文档
- **API接口文档**
- **代码架构说明**
- **测试用例文档**

### 10.3 运维文档
- **部署流程文档**
- **监控配置指南**
- **维护操作手册**

---

**注意**: 本需求规格书基于RouterOS API官方文档 (routeros-api 0.21.0) 和Debian 12环境要求编写，所有实现必须严格遵循官方规范，禁止使用推测或假想的实现方式。
