# Matrix Homeserver 项目结构说明

## 项目概述

本文档详细说明了高可用性分离式Matrix Homeserver部署项目的完整目录结构、文件组织方式和各组件的作用。项目采用标准化的目录结构，便于开发、部署和维护。

## 当前项目结构

```
synapse-matrix/                          # 项目根目录
├── README.md                            # 项目主要说明文档
├── config/                              # 配置文件目录
│   └── deployment.env.template          # 部署配置模板文件
└── docs/                                # 项目文档目录
    ├── 技术需求规格书.md                 # 详细技术规范文档
    ├── 实施计划.md                       # 项目实施计划和时间安排
    ├── 项目总结.md                       # 项目成果和价值总结
    └── 项目结构说明.md                   # 本文档
```

## 完整项目结构规划

以下是项目完整实施后的目录结构：

```
synapse-matrix/                          # 项目根目录
├── README.md                            # 项目主要说明文档
├── LICENSE                              # 项目许可证文件
├── .gitignore                           # Git忽略文件配置
├── .env.example                         # 环境变量示例文件
│
├── config/                              # 配置文件目录
│   ├── deployment.env.template          # 部署配置模板
│   ├── nginx.conf.template              # Nginx配置模板
│   ├── homeserver.yaml.template         # Synapse配置模板
│   ├── turnserver.conf.template         # Coturn配置模板
│   ├── postgres.conf.template           # PostgreSQL配置模板
│   └── redis.conf.template              # Redis配置模板
│
├── docker/                              # Docker相关文件
│   ├── docker-compose.yml               # 主要容器编排文件
│   ├── docker-compose.override.yml      # 开发环境覆盖配置
│   ├── docker-compose.prod.yml          # 生产环境配置
│   ├── Dockerfile.synapse               # Synapse自定义镜像
│   └── Dockerfile.nginx                 # Nginx自定义镜像
│
├── scripts/                             # 自动化脚本目录
│   ├── setup.sh                         # 初始化安装脚本
│   ├── ip_watchdog.sh                   # IP监控脚本
│   ├── certificate_manager.sh           # 证书管理脚本
│   ├── backup.sh                        # 备份脚本
│   ├── restore.sh                       # 恢复脚本
│   ├── health_check.sh                  # 健康检查脚本
│   ├── update.sh                        # 系统更新脚本
│   ├── validate_config.sh               # 配置验证脚本
│   └── utils/                           # 工具脚本子目录
│       ├── cloudflare_api.sh            # Cloudflare API工具
│       ├── logging.sh                   # 日志工具函数
│       ├── notification.sh              # 通知工具函数
│       └── common.sh                    # 通用工具函数
│
├── docs/                                # 项目文档目录
│   ├── 技术需求规格书.md                 # 详细技术规范
│   ├── 实施计划.md                       # 项目实施计划
│   ├── 项目总结.md                       # 项目成果总结
│   ├── 项目结构说明.md                   # 本文档
│   ├── 部署指南.md                       # 详细部署说明
│   ├── 运维手册.md                       # 运维操作指南
│   ├── 故障排除指南.md                   # 常见问题解决
│   ├── 安全配置指南.md                   # 安全加固说明
│   ├── 性能优化指南.md                   # 性能调优说明
│   ├── API文档.md                        # API接口文档
│   └── 更新日志.md                       # 版本更新记录
│
├── tests/                               # 测试文件目录
│   ├── unit/                            # 单元测试
│   │   ├── test_ip_watchdog.sh          # IP监控脚本测试
│   │   ├── test_certificate_manager.sh  # 证书管理测试
│   │   └── test_config_validation.sh    # 配置验证测试
│   ├── integration/                     # 集成测试
│   │   ├── test_federation.sh           # 联邦通信测试
│   │   ├── test_client_server.sh        # 客户端服务器测试
│   │   └── test_media_repo.sh           # 媒体仓库测试
│   ├── performance/                     # 性能测试
│   │   ├── load_test.py                 # 负载测试脚本
│   │   ├── stress_test.py               # 压力测试脚本
│   │   └── benchmark.py                 # 基准测试脚本
│   └── security/                        # 安全测试
│       ├── penetration_test.py          # 渗透测试脚本
│       ├── vulnerability_scan.py        # 漏洞扫描脚本
│       └── privacy_test.py              # 隐私保护测试
│
├── monitoring/                          # 监控配置目录
│   ├── prometheus/                      # Prometheus配置
│   │   ├── prometheus.yml               # Prometheus主配置
│   │   └── rules/                       # 告警规则目录
│   ├── grafana/                         # Grafana配置
│   │   ├── dashboards/                  # 仪表板配置
│   │   └── provisioning/                # 自动配置
│   ├── alertmanager/                    # 告警管理器配置
│   │   └── alertmanager.yml             # 告警配置
│   └── exporters/                       # 监控导出器
│       ├── node_exporter.yml            # 节点监控
│       └── postgres_exporter.yml        # 数据库监控
│
├── backup/                              # 备份相关文件
│   ├── backup_config.yml                # 备份配置
│   ├── retention_policy.yml             # 保留策略配置
│   └── restore_procedures.md            # 恢复流程文档
│
├── security/                            # 安全配置目录
│   ├── firewall/                        # 防火墙规则
│   │   ├── iptables.rules               # iptables规则
│   │   └── ufw.rules                    # UFW规则
│   ├── ssl/                             # SSL/TLS配置
│   │   ├── ssl_config.conf              # SSL配置参数
│   │   └── cipher_suites.conf           # 密码套件配置
│   └── policies/                        # 安全策略
│       ├── password_policy.yml          # 密码策略
│       └── access_control.yml           # 访问控制策略
│
├── examples/                            # 示例文件目录
│   ├── client_configs/                  # 客户端配置示例
│   │   ├── element.json                 # Element客户端配置
│   │   ├── nheko.conf                   # Nheko客户端配置
│   │   └── fluffychat.json              # FluffyChat配置
│   ├── federation/                      # 联邦配置示例
│   │   ├── well_known_client.json       # .well-known/matrix/client
│   │   └── well_known_server.json       # .well-known/matrix/server
│   └── deployment/                      # 部署示例
│       ├── development.env              # 开发环境配置
│       ├── staging.env                  # 测试环境配置
│       └── production.env               # 生产环境配置
│
├── tools/                               # 开发工具目录
│   ├── generate_config.py               # 配置生成工具
│   ├── migrate_data.py                  # 数据迁移工具
│   ├── user_management.py               # 用户管理工具
│   ├── room_management.py               # 房间管理工具
│   └── performance_analyzer.py          # 性能分析工具
│
└── data/                                # 运行时数据目录 (生产环境)
    ├── acme/                            # acme.sh证书存储
    ├── nginx/                           # Nginx数据
    │   ├── certs/                       # 证书软链接
    │   ├── conf/                        # 配置文件
    │   └── logs/                        # 访问日志
    ├── coturn/                          # Coturn数据
    │   ├── certs/                       # 证书软链接
    │   ├── conf/                        # 配置文件
    │   └── logs/                        # 运行日志
    ├── synapse/                         # Synapse数据
    │   ├── media/                       # 媒体文件存储
    │   ├── uploads/                     # 上传文件
    │   └── logs/                        # 应用日志
    ├── postgres/                        # PostgreSQL数据
    │   ├── data/                        # 数据库文件
    │   └── logs/                        # 数据库日志
    ├── redis/                           # Redis数据
    │   ├── data/                        # 缓存数据
    │   └── logs/                        # Redis日志
    ├── backup/                          # 备份文件
    │   ├── database/                    # 数据库备份
    │   ├── config/                      # 配置备份
    │   └── media/                       # 媒体文件备份
    └── logs/                            # 系统日志
        ├── ip_watchdog.log              # IP监控日志
        ├── certificate_manager.log      # 证书管理日志
        ├── health_check.log             # 健康检查日志
        └── system.log                   # 系统运行日志
```

## 目录功能说明

### 1. 根目录文件
- **README.md**: 项目主要说明文档，包含项目概述、快速开始指南
- **LICENSE**: 项目许可证文件，定义使用条款
- **.gitignore**: Git版本控制忽略文件配置
- **.env.example**: 环境变量示例文件，用于指导配置

### 2. config/ - 配置文件目录
存放所有服务的配置模板文件，支持变量替换和环境特定配置。

**关键文件**:
- `deployment.env.template`: 主要部署配置模板
- `*.conf.template`: 各服务的配置文件模板

### 3. docker/ - 容器化配置
包含Docker Compose文件和自定义镜像的Dockerfile。

**关键文件**:
- `docker-compose.yml`: 主要的容器编排文件
- `docker-compose.*.yml`: 环境特定的配置覆盖

### 4. scripts/ - 自动化脚本
核心的自动化运维脚本，实现系统的智能管理。

**核心脚本**:
- `ip_watchdog.sh`: 动态IP监控和同步
- `certificate_manager.sh`: 智能证书管理
- `setup.sh`: 一键初始化部署

### 5. docs/ - 项目文档
完整的项目文档集合，涵盖技术规范、操作指南等。

**主要文档**:
- 技术需求规格书: 详细的技术实现规范
- 实施计划: 项目执行路线图
- 运维手册: 日常运维操作指南

### 6. tests/ - 测试套件
全面的测试框架，包含单元测试、集成测试、性能测试等。

**测试类型**:
- 单元测试: 验证单个组件功能
- 集成测试: 验证组件间协作
- 性能测试: 验证系统性能指标
- 安全测试: 验证安全配置

### 7. monitoring/ - 监控配置
完整的监控和告警配置，支持Prometheus、Grafana等工具。

**监控组件**:
- Prometheus: 指标收集和存储
- Grafana: 可视化仪表板
- Alertmanager: 告警管理

### 8. security/ - 安全配置
安全加固相关的配置文件和策略定义。

**安全组件**:
- 防火墙规则: 网络安全配置
- SSL/TLS配置: 加密通信设置
- 安全策略: 访问控制和密码策略

### 9. tools/ - 开发工具
辅助开发和运维的工具脚本。

**工具类型**:
- 配置生成工具
- 数据迁移工具
- 用户管理工具
- 性能分析工具

### 10. data/ - 运行时数据
生产环境的数据存储目录，包含所有持久化数据。

**数据类型**:
- 应用数据: 数据库、缓存、媒体文件
- 配置数据: 运行时配置文件
- 日志数据: 系统和应用日志
- 备份数据: 定期备份文件

## 文件命名规范

### 1. 配置文件
- 模板文件: `*.template`
- 环境特定: `*.{env}.yml` (如 `docker-compose.prod.yml`)
- 示例文件: `*.example`

### 2. 脚本文件
- 主要脚本: 使用下划线分隔 (如 `ip_watchdog.sh`)
- 工具脚本: 按功能分组在子目录中
- 可执行权限: 所有脚本文件必须设置执行权限

### 3. 文档文件
- 中文文档: 使用中文文件名 (如 `技术需求规格书.md`)
- 英文文档: 使用英文文件名 (如 `deployment_guide.md`)
- 格式统一: 使用Markdown格式

### 4. 日志文件
- 应用日志: `{service_name}.log`
- 脚本日志: `{script_name}.log`
- 系统日志: `system.log`

## 权限管理

### 1. 文件权限
- 配置文件: `644` (rw-r--r--)
- 敏感配置: `600` (rw-------)
- 脚本文件: `755` (rwxr-xr-x)
- 数据目录: `755` (rwxr-xr-x)

### 2. 目录权限
- 一般目录: `755` (rwxr-xr-x)
- 数据目录: `750` (rwxr-x---)
- 日志目录: `755` (rwxr-xr-x)
- 备份目录: `700` (rwx------)

### 3. 用户和组
- 服务用户: 为每个服务创建专用用户
- 管理组: 创建管理员组统一管理权限
- 最小权限: 遵循最小权限原则

## 版本控制

### 1. Git配置
- `.gitignore`: 忽略敏感文件和临时文件
- 分支策略: 使用Git Flow工作流
- 提交规范: 遵循约定式提交规范

### 2. 忽略文件
```gitignore
# 敏感配置文件
*.env
!*.env.example
!*.env.template

# 运行时数据
data/
logs/
*.log

# 临时文件
*.tmp
*.bak
.DS_Store
```

### 3. 标签管理
- 版本标签: `v1.0.0` 格式
- 发布标签: `release-*` 格式
- 里程碑标签: `milestone-*` 格式

## 部署环境

### 1. 开发环境
- 本地开发: 使用Docker Compose快速启动
- 配置简化: 使用默认配置和测试数据
- 调试支持: 启用详细日志和调试模式

### 2. 测试环境
- 功能测试: 完整功能验证环境
- 性能测试: 模拟生产负载的测试环境
- 安全测试: 安全配置验证环境

### 3. 生产环境
- 高可用: 多节点部署和负载均衡
- 监控完整: 全方位监控和告警
- 备份策略: 完善的备份和恢复机制

本项目结构设计遵循了软件工程的最佳实践，通过清晰的目录组织和标准化的文件命名，为项目的开发、部署和维护提供了良好的基础。
