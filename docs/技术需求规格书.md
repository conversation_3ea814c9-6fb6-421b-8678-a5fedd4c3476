# 高可用性分离式Matrix Homeserver部署 - 技术需求规格书

**文档版本**: Final  
**目标读者**: 后端/DevOps开发人员  
**创建日期**: 2025-01-13  

## 1. 项目目标与范围

### 1.1 项目目标
部署一个功能完备、性能可靠、可扩展、**具备弹性运维能力**的自托管Matrix通讯服务。本项目的核心是确保系统的长期稳定运行，通过精细化的自动化管理，最大限度地减少手动干预，并规避常见的运维陷阱。

### 1.2 核心约束条件
- **动态公网IP**: 核心服务托管于局域网，其公网IP非固定，IP变更频率不可预测
- **ISP端口封禁**: 标准的HTTP(80)和HTTPS(443)端口被ISP封禁，所有公网服务必须使用自定义端口
- **NAT环境限制**: 内部服务器位于NAT网络后，需要路由器端口转发支持
- **服务分离架构**: 必须通过一个公网静态IP的VPS作为"指路牌"(Signpost)，引导客户端和联邦服务器正确找到位于动态IP上的核心服务
- **隐私最大化**: 禁止任何与第三方身份服务器的集成，用户发现机制严格限制在Matrix ID层面
- **成本控制**: 最小化VPS资源使用，优化成本效益比
- **运维简化**: 减少手动干预，实现高度自动化的运维管理

### 1.3 弹性运维要求
- **证书管理**: 智能地管理证书生命周期，避免触及Let's Encrypt的速率限制，确保更新过程不中断服务
- **配置解耦**: 服务配置与证书文件存储必须解耦，提高系统的健壮性和可维护性
- **自动化运维**: 通过脚本自动化处理IP变更、证书续期、服务重载等运维任务

## 2. 系统架构设计

### 2.1 分离式架构概述
本系统采用分离式部署架构，将服务发现和实际服务分离：

```
[客户端/联邦服务器]
    ↓ 1. 查询主域名 (DNS A记录 → Signpost VPS静态IP)
[Signpost VPS - 静态IP:80/443]
    ↓ 2. 返回.well-known文件 (指向子域名:自定义端口)
[LAN Server - 动态IP:自定义端口]
    ↓ 3. 实际Matrix服务处理
[Docker容器集群: Synapse + PostgreSQL + Redis + Coturn + Nginx]
```

### 2.2 组件分布

#### 2.2.1 外部指路牌 (Signpost VPS)
- **角色**: 静态Web服务器，作为Matrix联邦的稳定入口点
- **域名**: 托管主域名 `[DOMAIN]`
- **IP地址**: 静态公网IP
- **端口**: 标准80/443端口
- **功能**: 通过Nginx提供`.well-known`文件，指引流量到内部服务器

#### 2.2.2 内部核心服务 (LAN Server)
- **角色**: 运行所有Matrix核心服务的计算节点
- **域名**: 托管服务子域名 `[SUBDOMAIN_MATRIX].[DOMAIN]`
- **IP地址**: 动态公网IP
- **端口**: 自定义HTTPS端口 `[HTTPS_PORT]`
- **功能**: 通过Docker Compose运行完整的Matrix服务栈
- **部署路径**: 统一存放于可自定义的部署主目录（默认为`/opt/matrix`）

### 2.3 流量解析流程
1. 客户端或联邦服务器查询主域名以寻找Matrix服务
2. 主域名的DNS A记录指向Signpost VPS的静态IP
3. Signpost VPS上的Nginx响应`/.well-known/matrix/`请求，返回JSON对象指向内部服务器
4. 客户端根据JSON指引，向内部子域名发起连接
5. 内部子域名的DNS A记录由自动化脚本动态更新，指向LAN Server的当前公网IP
6. LAN Server的路由器将自定义端口流量转发给内部Nginx，最终代理到Synapse服务

## 3. 技术栈与组件规格

### 3.1 核心组件清单

| 组件 | 技术选型 | 版本 | 容器化 | 角色与关键要求 |
|------|----------|------|--------|----------------|
| Homeserver | Synapse | latest stable | 是 | 核心通讯服务，支持联邦和端到端加密 |
| 数据库 | PostgreSQL | 15-alpine | 是 | 主数据库，保证高并发性能 |
| 缓存/总线 | Redis | 7-alpine | 是 | 卸载事件流和缓存，提升响应速度 |
| TURN/STUN | Coturn | latest stable | 是 | WebRTC NAT穿透，支持音视频通话 |
| 反向代理 | Nginx | alpine | 是 | TLS终止和请求代理 |
| 证书管理 | acme.sh | latest | 否(主机) | 通过DNS-01方式申请Let's Encrypt证书 |
| 自动化脚本 | Bash/Python | - | 否(主机) | 动态IP检测与同步、证书管理 |
| 容器化平台 | Docker Compose | latest | - | 基础运行环境 |

### 3.2 性能与资源要求

#### 3.2.1 Signpost VPS
- **CPU**: 1核心
- **内存**: 1GB
- **存储**: 20GB SSD
- **网络**: 静态公网IP，标准80/443端口

#### 3.2.2 LAN Server
- **CPU**: 4核心（推荐8核心）
- **内存**: 8GB（推荐16GB）
- **存储**: 100GB SSD（数据目录），推荐200GB以上
- **网络**: 动态公网IP，支持端口转发
- **路由器要求**: 支持NAT端口转发配置，支持UPnP或手动配置
- **带宽要求**: 上行带宽至少10Mbps，推荐50Mbps以上
- **操作系统**: Ubuntu 20.04 LTS或更新版本，CentOS 8或更新版本

## 4. 详细配置需求

### 4.1 Signpost VPS配置

#### 4.1.1 Nginx配置要求
- 监听主域名的80和443端口
- 使用certbot或同类工具为主域名申请并自动续期SSL证书
- 提供Matrix联邦发现所需的`.well-known`文件

#### 4.1.2 .well-known文件内容规范

**`.well-known/matrix/client`**:
```json
{
    "m.homeserver": { 
        "base_url": "https://[SUBDOMAIN_MATRIX].[DOMAIN]:[HTTPS_PORT]" 
    },
    "io.element.e2ee": { 
        "default": true 
    },
    "io.element.integrations": { 
        "url": "https://scalar.vector.im/" 
    }
}
```

**`.well-known/matrix/server`**:
```json
{ 
    "m.server": "[SUBDOMAIN_MATRIX].[DOMAIN]:[HTTPS_PORT]" 
}
```

**重要说明**: `m.identity_server`配置项被**有意地完全移除**，以确保隐私最大化。

### 4.2 LAN Server - Docker Compose配置

#### 4.2.1 服务组成要求
- 必须包含`synapse`, `db`(postgres), `redis`, `coturn`, `nginx`五个服务
- `synapse`服务必须`depends_on: [db, redis]`
- 所有数据卷必须挂载到主机部署主目录下对应的子目录

#### 4.2.2 端口映射规范
```yaml
services:
  nginx:
    ports:
      - "127.0.0.1:[HTTPS_PORT]:[HTTPS_PORT]"  # 仅本地绑定
  
  coturn:
    ports:
      - "3478:3478/tcp"
      - "3478:3478/udp"
      - "5349:5349/tcp"
      - "[COTURN_MIN_PORT]-[COTURN_MAX_PORT]:[COTURN_MIN_PORT]-[COTURN_MAX_PORT]/udp"
```

#### 4.2.3 证书卷挂载要求
- `nginx`和`coturn`服务必须额外挂载一个只读的证书目录卷
- 该目录仅用于存放证书的软链接，不直接存储证书文件
- 软链接指向由`acme.sh`管理的实际证书文件

### 4.3 核心服务配置规范

#### 4.3.1 Synapse配置 (`homeserver.yaml`)
```yaml
server_name: "[DOMAIN]"  # 必须是主域名

database:
  name: psycopg2
  args:
    host: db
    port: 5432
    database: synapse
    user: synapse
    password: "[DB_PASSWORD]"

redis:
  enabled: true
  host: redis
  port: 6379

turn_uris:
  - "turn:[SUBDOMAIN_MATRIX].[DOMAIN]:[TURN_PORT]?transport=udp"
  - "turn:[SUBDOMAIN_MATRIX].[DOMAIN]:[TURN_PORT]?transport=tcp"
  - "turns:[SUBDOMAIN_MATRIX].[DOMAIN]:[TURNS_PORT]?transport=tcp"

# 隐私强化配置
enable_3pid_lookup: false
allow_guest_access: false
enable_registration: false  # 或根据需要配置
```

#### 4.3.2 Nginx配置 (`nginx.conf`)
```nginx
server {
    listen [HTTPS_PORT] ssl http2;
    server_name [SUBDOMAIN_MATRIX].[DOMAIN];
    
    # 证书路径必须指向稳定的软链接
    ssl_certificate /etc/nginx/certs/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/private.key;
    
    # SSL优化配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Matrix特定配置
    location ~ ^(/_matrix|/_synapse/client) {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
    }
}
```

#### 4.3.3 Coturn配置 (`turnserver.conf`)
```conf
# 动态配置项 - 由脚本自动更新
external-ip=[CURRENT_PUBLIC_IP]
min-port=[COTURN_MIN_PORT]
max-port=[COTURN_MAX_PORT]

# 静态配置项
realm=[DOMAIN]
listening-port=3478
tls-listening-port=5349

# 证书路径必须指向稳定的软链接
cert=/etc/coturn/certs/fullchain.cer
pkey=/etc/coturn/certs/private.key

# 安全配置
no-stdout-log
log-file=/var/log/coturn/coturn.log
pidfile=/var/run/coturn/coturn.pid
```

## 5. 自动化运维脚本设计

### 5.1 设计原则

#### 5.1.1 模块化原则
- **职责分离**: 自动化任务由两个独立的脚本执行
  - 高频IP监控脚本 (`ip_watchdog.sh`)
  - 低频证书管理脚本 (`certificate_manager.sh`)
- **配置解耦**: 脚本配置与服务配置分离，避免相互影响
- **错误隔离**: 单个脚本的失败不应影响其他脚本的正常运行

#### 5.1.2 可配置性原则
- 所有脚本必须将关键参数定义为可配置的变量
- 提供合理的默认值，支持环境变量覆盖
- 支持命令行参数传递，便于调试和测试

### 5.2 脚本一: 动态IP监控器 (`ip_watchdog.sh`)

#### 5.2.1 核心职责
- **唯一职责**: 监控和同步动态公网IP变化
- **执行频率**: Cron Job高频执行（推荐每分钟）
- **影响范围**: DNS记录、Coturn配置

#### 5.2.2 执行逻辑
```bash
#!/bin/bash
# 1. 获取当前公网IP
CURRENT_IP=$(curl -s https://ipv4.icanhazip.com/)

# 2. 读取上次记录的IP
LAST_IP=$(cat /opt/matrix/data/last_ip.txt 2>/dev/null || echo "")

# 3. 比较IP是否发生变化
if [ "$CURRENT_IP" != "$LAST_IP" ]; then
    # 4. 更新Cloudflare DNS记录
    update_cloudflare_dns "$CURRENT_IP"

    # 5. 更新Coturn配置中的external-ip
    update_coturn_config "$CURRENT_IP"

    # 6. 重启Coturn服务
    docker compose restart coturn

    # 7. 记录新IP
    echo "$CURRENT_IP" > /opt/matrix/data/last_ip.txt

    # 8. 记录日志
    log_info "IP changed from $LAST_IP to $CURRENT_IP"
fi
```

#### 5.2.3 关键配置参数
```bash
# 可配置变量
DEPLOY_DIR="/opt/matrix"
DOMAIN="example.com"
SUBDOMAIN_MATRIX="matrix"
CLOUDFLARE_API_TOKEN="your_token_here"
CLOUDFLARE_ZONE_ID="your_zone_id_here"
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"
```

### 5.3 脚本二: 智能证书管理器 (`certificate_manager.sh`)

#### 5.3.1 核心职责
- **主要职责**: 证书的申请、续期和部署
- **执行频率**: Cron Job低频执行（推荐每天凌晨）
- **智能特性**: 基于证书有效期的条件触发

#### 5.3.2 智能续期逻辑
```bash
#!/bin/bash
# 1. 检查证书有效期
check_certificate_validity() {
    local domain=$1
    local cert_file="/opt/matrix/data/acme/${domain}/fullchain.cer"

    if [ ! -f "$cert_file" ]; then
        echo "MISSING"
        return
    fi

    local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_left=$(( (expiry_epoch - current_epoch) / 86400 ))

    echo "$days_left"
}

# 2. 条件触发续期
for domain in "$SUBDOMAIN_MATRIX.$DOMAIN"; do
    days_left=$(check_certificate_validity "$domain")

    if [ "$days_left" = "MISSING" ] || [ "$days_left" -lt 14 ]; then
        log_info "Certificate for $domain needs renewal (days left: $days_left)"

        # 3. 申请/续期证书
        acme.sh --issue --dns dns_cf \
            -d "$domain" \
            --cert-home "$DEPLOY_DIR/data/acme"

        if [ $? -eq 0 ]; then
            # 4. 部署证书（更新软链接）
            deploy_certificate "$domain"

            # 5. 重载相关服务
            reload_services

            log_info "Certificate for $domain renewed successfully"
        else
            log_error "Failed to renew certificate for $domain"
        fi
    else
        log_info "Certificate for $domain is valid for $days_left days"
    fi
done
```

#### 5.3.3 证书部署机制
```bash
deploy_certificate() {
    local domain=$1
    local acme_dir="$DEPLOY_DIR/data/acme/$domain"

    # 原子化更新软链接
    ln -sf "$acme_dir/fullchain.cer" "$DEPLOY_DIR/data/nginx/certs/fullchain.cer"
    ln -sf "$acme_dir/$domain.key" "$DEPLOY_DIR/data/nginx/certs/private.key"
    ln -sf "$acme_dir/fullchain.cer" "$DEPLOY_DIR/data/coturn/certs/fullchain.cer"
    ln -sf "$acme_dir/$domain.key" "$DEPLOY_DIR/data/coturn/certs/private.key"

    log_info "Certificate symlinks updated for $domain"
}

reload_services() {
    # 平滑重载Nginx
    docker compose exec nginx nginx -s reload

    # 重启Coturn（Coturn不支持热重载）
    docker compose restart coturn

    log_info "Services reloaded after certificate update"
}
```

### 5.4 证书管理与目录结构设计

#### 5.4.1 解耦原则
- **存储与使用分离**: 证书的存储（由`acme.sh`管理）与使用（由服务配置决定）完全分离
- **稳定路径**: 服务通过路径稳定不变的软链接引用证书
- **原子更新**: 通过`ln -sf`命令实现证书更新的原子性

#### 5.4.2 目录结构规范
```
[DEPLOY_DIR]/
├── data/
│   ├── acme/                          # acme.sh的证书存储 (Source of Truth)
│   │   └── [SUBDOMAIN_MATRIX].[DOMAIN]/
│   │       ├── fullchain.cer          # 完整证书链
│   │       ├── [DOMAIN].key           # 私钥
│   │       └── ca.cer                 # CA证书
│   ├── nginx/
│   │   ├── certs/                     # 存放指向acme目录的软链接
│   │   │   ├── fullchain.cer -> ../../acme/[DOMAIN]/fullchain.cer
│   │   │   └── private.key -> ../../acme/[DOMAIN]/[DOMAIN].key
│   │   └── conf/                      # Nginx配置文件
│   ├── coturn/
│   │   ├── certs/                     # 存放软链接
│   │   │   ├── fullchain.cer -> ../../acme/[DOMAIN]/fullchain.cer
│   │   │   └── private.key -> ../../acme/[DOMAIN]/[DOMAIN].key
│   │   └── conf/                      # Coturn配置文件
│   ├── synapse/                       # Synapse数据和配置
│   ├── postgres/                      # PostgreSQL数据
│   ├── redis/                         # Redis数据
│   └── last_ip.txt                    # 记录上次IP
├── scripts/
│   ├── ip_watchdog.sh                 # IP监控脚本
│   ├── certificate_manager.sh         # 证书管理脚本
│   └── setup.sh                       # 初始化脚本
├── config/
│   ├── nginx.conf.template            # Nginx配置模板
│   ├── homeserver.yaml.template       # Synapse配置模板
│   └── turnserver.conf.template       # Coturn配置模板
└── docker-compose.yml                 # 容器编排配置
```

## 6. 网络配置与防火墙规则

### 6.1 RouterOS端口转发配置

必须在路由器的NAT表中配置以下端口转发规则：

| 协议 | 外部端口 | 内部目标端口 | 内部IP | 服务 | 备注 |
|------|----------|--------------|--------|------|------|
| TCP | [HTTPS_PORT] | [HTTPS_PORT] | [LAN_SERVER_IP] | Nginx | Synapse HTTPS服务 |
| TCP | 5349 | 5349 | [LAN_SERVER_IP] | Coturn | TURN over TLS |
| TCP | 3478 | 3478 | [LAN_SERVER_IP] | Coturn | TURN/STUN TCP |
| UDP | 3478 | 3478 | [LAN_SERVER_IP] | Coturn | TURN/STUN UDP |
| UDP | [COTURN_MIN_PORT]-[COTURN_MAX_PORT] | [COTURN_MIN_PORT]-[COTURN_MAX_PORT] | [LAN_SERVER_IP] | Coturn | 媒体转发端口段 |

**重要配置说明**:
- `[LAN_SERVER_IP]`: 内网服务器的固定IP地址（建议设置为静态IP）
- `[HTTPS_PORT]`: 默认8443，可根据ISP限制调整
- `[COTURN_MIN_PORT]-[COTURN_MAX_PORT]`: 默认49152-65535，可根据需要调整范围
- 所有端口转发规则必须指向同一台内网服务器
- 建议在路由器中为内网服务器设置DHCP保留，确保IP地址固定

### 6.2 防火墙安全规则

#### 6.2.1 入站规则
```bash
# 仅允许必要端口的入站连接
iptables -A INPUT -p tcp --dport [HTTPS_PORT] -j ACCEPT
iptables -A INPUT -p tcp --dport 3478 -j ACCEPT
iptables -A INPUT -p udp --dport 3478 -j ACCEPT
iptables -A INPUT -p tcp --dport 5349 -j ACCEPT
iptables -A INPUT -p udp --dport [COTURN_MIN_PORT]:[COTURN_MAX_PORT] -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 默认拒绝其他入站连接
iptables -P INPUT DROP
```

#### 6.2.2 出站规则
```bash
# 允许DNS查询
iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT

# 允许HTTP/HTTPS出站（用于证书申请和API调用）
iptables -A OUTPUT -p tcp --dport 80 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT

# 允许Matrix联邦端口
iptables -A OUTPUT -p tcp --dport 8448 -j ACCEPT

# 默认允许出站连接
iptables -P OUTPUT ACCEPT
```

## 7. 部署流程与验证

### 7.1 初始化部署流程

#### 7.1.1 环境准备
1. **基础环境设置**
   ```bash
   # 安装Docker和Docker Compose
   curl -fsSL https://get.docker.com | sh
   sudo usermod -aG docker $USER

   # 安装acme.sh
   curl https://get.acme.sh | sh
   source ~/.bashrc
   ```

2. **创建部署目录结构**
   ```bash
   sudo mkdir -p /opt/matrix/{data,scripts,config}
   sudo mkdir -p /opt/matrix/data/{acme,nginx/certs,coturn/certs,synapse,postgres,redis}
   sudo chown -R $USER:$USER /opt/matrix
   ```

3. **配置环境变量**
   ```bash
   # 创建环境配置文件
   cat > /opt/matrix/.env << EOF
   DOMAIN=example.com
   SUBDOMAIN_MATRIX=matrix
   HTTPS_PORT=8443
   COTURN_MIN_PORT=49152
   COTURN_MAX_PORT=65535
   CLOUDFLARE_API_TOKEN=your_token_here
   CLOUDFLARE_ZONE_ID=your_zone_id_here
   DB_PASSWORD=$(openssl rand -base64 32)
   EOF
   ```

#### 7.1.2 初始化执行
1. **首次IP同步**
   ```bash
   cd /opt/matrix
   ./scripts/ip_watchdog.sh
   ```

2. **首次证书申请**
   ```bash
   ./scripts/certificate_manager.sh
   ```

3. **启动服务**
   ```bash
   docker compose up -d
   ```

#### 7.1.3 配置定时任务
```bash
# 编辑crontab
crontab -e

# 添加以下任务
# IP监控 - 每分钟执行
* * * * * /opt/matrix/scripts/ip_watchdog.sh >> /var/log/matrix/ip_watchdog.log 2>&1

# 证书管理 - 每天凌晨2点执行
0 2 * * * /opt/matrix/scripts/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1
```

### 7.2 功能验证清单

#### 7.2.1 基础功能验证
- [ ] **用户注册与登录**: 成功创建用户账户并登录
- [ ] **消息收发**: 能够在房间内发送和接收文本消息
- [ ] **文件传输**: 能够上传和下载文件附件
- [ ] **联邦通信**: 能够与其他Matrix服务器的用户通信
- [ ] **音视频通话**: WebRTC通话功能正常工作

#### 7.2.2 隐私性验证
- [ ] **身份服务器禁用**: 确认无法通过邮箱/手机号搜索用户
- [ ] **用户发现限制**: 只能通过完整的Matrix ID找到用户
- [ ] **端到端加密**: 默认启用E2EE，消息内容加密传输
- [ ] **元数据保护**: 最小化暴露用户活动元数据

#### 7.2.3 健壮性验证
- [ ] **证书软链接**: 检查软链接是否存在且指向正确的证书文件
- [ ] **证书续期测试**: 模拟证书续期，验证服务平滑重载
- [ ] **配置解耦测试**: 删除证书目录，验证软链接能被重新创建
- [ ] **服务恢复**: 验证容器重启后服务能正常恢复

#### 7.2.4 容灾验证
- [ ] **IP变更测试**: 手动模拟IP变更，验证自动化脚本响应
- [ ] **DNS更新验证**: 确认Cloudflare DNS记录被正确更新
- [ ] **Coturn配置更新**: 验证external-ip配置被正确修改
- [ ] **服务连续性**: 确认IP变更过程中服务不中断

### 7.3 性能基准测试

#### 7.3.1 负载测试指标
- **并发用户数**: 支持的同时在线用户数量
- **消息吞吐量**: 每秒处理的消息数量
- **响应时间**: API请求的平均响应时间
- **资源使用率**: CPU、内存、磁盘I/O使用情况

#### 7.3.2 监控指标
- **服务可用性**: 99.9%以上的服务可用时间
- **证书有效性**: 证书到期前自动续期成功率
- **IP同步延迟**: IP变更到DNS更新完成的时间
- **联邦连通性**: 与外部Matrix服务器的连接成功率

## 8. 运维监控与告警

### 8.1 日志管理

#### 8.1.1 日志分类
- **应用日志**: Synapse、Nginx、Coturn的运行日志
- **系统日志**: Docker容器和主机系统日志
- **脚本日志**: 自动化脚本的执行日志
- **安全日志**: 认证失败、异常访问等安全事件

#### 8.1.2 日志轮转配置
```bash
# /etc/logrotate.d/matrix
/var/log/matrix/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 matrix matrix
    postrotate
        docker compose exec nginx nginx -s reopen
    endscript
}
```

### 8.2 监控告警

#### 8.2.1 关键监控指标
- **服务健康状态**: 所有容器的运行状态
- **证书有效期**: 距离到期时间少于7天时告警
- **磁盘空间**: 使用率超过80%时告警
- **内存使用**: 使用率超过90%时告警
- **网络连通性**: 外部访问失败时告警

#### 8.2.2 告警通知机制
```bash
# 示例告警脚本
send_alert() {
    local message=$1
    local severity=$2

    # 发送邮件告警
    echo "$message" | mail -s "Matrix Server Alert [$severity]" <EMAIL>

    # 发送Webhook通知（如Slack、Discord等）
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"$message\"}" \
        "$WEBHOOK_URL"
}
```

## 9. 备份与恢复策略

### 9.1 备份策略

#### 9.1.1 数据备份范围
- **数据库备份**: PostgreSQL完整数据库
- **配置备份**: 所有配置文件和环境变量
- **证书备份**: acme.sh管理的所有证书
- **媒体文件**: 用户上传的文件和媒体内容

#### 9.1.2 备份频率与保留策略
- **数据库**: 每日全量备份，保留30天
- **配置文件**: 每次修改后立即备份
- **证书**: 每次更新后自动备份
- **媒体文件**: 每周增量备份，每月全量备份

### 9.2 恢复流程

#### 9.2.1 灾难恢复步骤
1. **环境重建**: 重新部署基础环境和依赖
2. **配置恢复**: 恢复所有配置文件和环境变量
3. **数据库恢复**: 从备份恢复PostgreSQL数据
4. **证书恢复**: 恢复证书文件并重建软链接
5. **服务启动**: 启动所有服务并验证功能

#### 9.2.2 恢复验证清单
- [ ] 所有服务正常启动
- [ ] 用户能够正常登录
- [ ] 历史消息完整可见
- [ ] 联邦通信正常
- [ ] 证书有效且自动续期正常

## 10. 安全加固措施

### 10.1 系统安全

#### 10.1.1 主机安全
- **最小权限原则**: 服务以非root用户运行
- **防火墙配置**: 严格限制入站和出站连接
- **系统更新**: 定期更新操作系统和软件包
- **SSH安全**: 禁用密码登录，仅允许密钥认证

#### 10.1.2 容器安全
- **镜像安全**: 使用官方镜像，定期更新到最新版本
- **资源限制**: 为每个容器设置CPU和内存限制
- **网络隔离**: 使用Docker网络隔离不同服务
- **只读文件系统**: 尽可能使用只读根文件系统

### 10.2 应用安全

#### 10.2.1 Matrix安全配置
- **强密码策略**: 强制用户使用复杂密码
- **速率限制**: 限制API请求频率，防止滥用
- **媒体限制**: 限制上传文件大小和类型
- **房间权限**: 合理配置房间创建和邀请权限

#### 10.2.2 TLS安全
- **协议版本**: 仅支持TLS 1.2和1.3
- **密码套件**: 使用强加密算法，禁用弱密码
- **HSTS**: 启用HTTP严格传输安全
- **证书透明度**: 启用证书透明度日志

## 11. 扩展性考虑

### 11.1 水平扩展

#### 11.1.1 数据库扩展
- **读写分离**: 配置PostgreSQL主从复制
- **连接池**: 使用pgbouncer等连接池工具
- **分片策略**: 根据用户或房间进行数据分片

#### 11.1.2 应用扩展
- **负载均衡**: 在多个Synapse实例前部署负载均衡器
- **缓存扩展**: Redis集群或分布式缓存
- **媒体存储**: 使用对象存储服务存储媒体文件

### 11.2 垂直扩展

#### 11.2.1 性能优化
- **数据库调优**: 优化PostgreSQL配置参数
- **缓存策略**: 增加Redis内存，优化缓存策略
- **网络优化**: 调整TCP参数，优化网络性能

#### 11.2.2 资源监控
- **性能指标**: 监控CPU、内存、磁盘、网络使用情况
- **瓶颈识别**: 识别系统性能瓶颈并及时优化
- **容量规划**: 根据使用增长预测资源需求

---

## 12. 配置参数完整定义

### 12.1 必需配置参数

| 参数名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| DOMAIN | string | - | 主域名 | example.com |
| SUBDOMAIN_MATRIX | string | matrix | Matrix服务子域名 | matrix |
| HTTPS_PORT | integer | 8443 | 自定义HTTPS端口 | 8443 |
| CLOUDFLARE_API_TOKEN | string | - | Cloudflare API令牌 | abc123... |
| CLOUDFLARE_ZONE_ID | string | - | Cloudflare区域ID | def456... |
| DB_PASSWORD | string | - | 数据库密码 | 随机生成 |
| COTURN_SHARED_SECRET | string | - | Coturn共享密钥 | 随机生成 |

### 12.2 可选配置参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| DEPLOY_DIR | string | /opt/matrix | 部署根目录 |
| COTURN_MIN_PORT | integer | 49152 | Coturn最小端口 |
| COTURN_MAX_PORT | integer | 65535 | Coturn最大端口 |
| CERT_RENEWAL_THRESHOLD | integer | 14 | 证书续期阈值(天) |
| IP_CHECK_INTERVAL | integer | 1 | IP检查间隔(分钟) |
| LOG_LEVEL | string | INFO | 日志级别 |

### 12.3 安全配置要求

#### 12.3.1 密码强度要求
- **数据库密码**: 至少32字符，包含大小写字母、数字和特殊字符
- **Coturn共享密钥**: 至少32字符随机字符串
- **Synapse签名密钥**: 由Synapse自动生成，不可手动设置

#### 12.3.2 文件权限要求
```bash
# 配置文件权限
chmod 600 config/deployment.env
chmod 644 config/*.template
chmod 755 scripts/*.sh

# 数据目录权限
chmod 750 data/
chmod 700 data/acme/
chmod 755 data/nginx/certs/
chmod 755 data/coturn/certs/
```

#### 12.3.3 网络安全要求
- **防火墙**: 仅开放必要端口，默认拒绝所有入站连接
- **SSH安全**: 禁用密码登录，仅允许密钥认证
- **SSL/TLS**: 仅支持TLS 1.2和1.3，禁用弱密码套件
- **访问控制**: 实施基于IP的访问控制（可选）

## 13. 故障处理和恢复

### 13.1 常见故障场景

#### 13.1.1 IP变更故障
**症状**: 客户端无法连接，联邦通信中断
**原因**: 动态IP变更，DNS记录未及时更新
**解决方案**:
```bash
# 手动触发IP更新
./scripts/ip_watchdog.sh --force-update

# 检查DNS解析
nslookup matrix.example.com

# 验证服务可达性
curl -k https://matrix.example.com:8443/_matrix/client/versions
```

#### 13.1.2 证书过期故障
**症状**: HTTPS连接失败，浏览器显示证书错误
**原因**: 证书过期，自动续期失败
**解决方案**:
```bash
# 手动续期证书
./scripts/certificate_manager.sh --force-renew

# 检查证书有效期
openssl x509 -in data/nginx/certs/fullchain.cer -noout -dates

# 重启相关服务
docker compose restart nginx coturn
```

#### 13.1.3 服务启动故障
**症状**: 容器无法启动或频繁重启
**原因**: 配置错误、资源不足、端口冲突
**解决方案**:
```bash
# 检查容器状态
docker compose ps
docker compose logs [service_name]

# 验证配置文件
./scripts/validate_config.sh

# 检查资源使用
docker stats
df -h
free -h
```

### 13.2 灾难恢复流程

#### 13.2.1 完全重建流程
1. **环境准备**: 重新安装操作系统和基础软件
2. **配置恢复**: 从备份恢复配置文件
3. **数据恢复**: 恢复数据库和媒体文件
4. **证书恢复**: 恢复或重新申请SSL证书
5. **服务启动**: 启动所有服务并验证功能
6. **DNS更新**: 更新DNS记录指向新IP

#### 13.2.2 数据备份策略
```bash
# 数据库备份
docker compose exec db pg_dump -U synapse synapse > backup/db_$(date +%Y%m%d).sql

# 配置备份
tar -czf backup/config_$(date +%Y%m%d).tar.gz config/ data/nginx/conf/ data/coturn/conf/

# 媒体文件备份
rsync -av data/synapse/media/ backup/media/

# 证书备份
tar -czf backup/certs_$(date +%Y%m%d).tar.gz data/acme/
```

---

**文档结束**

本技术需求规格书为高可用性分离式Matrix Homeserver的部署提供了全面的技术指导。通过遵循本文档的要求和建议，可以构建一个稳定、安全、可扩展的Matrix通讯服务，满足生产环境的严格要求。

**版本历史**:
- v1.0 (2025-01-13): 初始版本，包含基础架构设计
- v1.1 (2025-01-13): 完善配置参数定义、安全要求和故障处理流程
