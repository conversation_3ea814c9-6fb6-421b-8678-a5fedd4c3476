# Matrix Homeserver 证书管理说明

## 概述

本文档详细说明Matrix Homeserver项目中SSL证书的管理策略，包括存储位置、符号链接结构和部署弹性设计。

## 证书管理架构

### 设计原则

1. **单一真实来源**: 证书的主要存储位置为acme.sh的默认目录 `/root/.acme.sh/`
2. **符号链接部署**: 部署目录中使用符号链接指向主存储，而不是复制文件
3. **部署弹性**: 重新部署时不影响原始证书文件
4. **原子化更新**: 通过符号链接实现证书更新的原子性

### 证书存储层次

```
层次1: acme.sh主存储 (/root/.acme.sh/)
  ↓ 符号链接
层次2: 部署目录链接 (/opt/matrix/data/acme/)
  ↓ 符号链接  
层次3: 服务目录链接 (/opt/matrix/data/nginx/certs/, /opt/matrix/data/coturn/certs/)
```

## 目录结构详解

### 1. acme.sh主存储 (`/root/.acme.sh/`)

这是证书的**真实来源**，由acme.sh直接管理：

```
/root/.acme.sh/
├── matrix.example.com_ecc/          # ECC证书 (优先使用)
│   ├── fullchain.cer                # 完整证书链
│   ├── matrix.example.com.key       # 私钥
│   ├── ca.cer                       # CA证书
│   └── matrix.example.com.cer       # 域名证书
├── matrix.example.com/              # RSA证书 (备选)
│   ├── fullchain.cer
│   ├── matrix.example.com.key
│   ├── ca.cer
│   └── matrix.example.com.cer
└── account.conf                     # acme.sh配置
```

**特点**:
- 由acme.sh自动管理
- 证书续期时自动更新
- 重新部署项目时不受影响
- 权限: `700` (仅root可访问)

### 2. 部署目录链接 (`/opt/matrix/data/acme/`)

这是部署目录中的**中间层链接**：

```
/opt/matrix/data/acme/
└── matrix.example.com/
    ├── fullchain.cer -> /root/.acme.sh/matrix.example.com_ecc/fullchain.cer
    ├── private.key -> /root/.acme.sh/matrix.example.com_ecc/matrix.example.com.key
    └── ca.cer -> /root/.acme.sh/matrix.example.com_ecc/ca.cer
```

**特点**:
- 符号链接指向acme.sh主存储
- 提供统一的证书访问接口
- 重新部署时可以重建
- 权限: `755` (可读)

### 3. 服务目录链接

#### Nginx证书目录 (`/opt/matrix/data/nginx/certs/`)

```
/opt/matrix/data/nginx/certs/
├── fullchain.cer -> ../../acme/matrix.example.com/fullchain.cer
└── private.key -> ../../acme/matrix.example.com/private.key
```

#### Coturn证书目录 (`/opt/matrix/data/coturn/certs/`)

```
/opt/matrix/data/coturn/certs/
├── fullchain.cer -> ../../acme/matrix.example.com/fullchain.cer
└── private.key -> ../../acme/matrix.example.com/private.key
```

**特点**:
- 符号链接指向部署目录的中间层
- 服务配置文件引用这些路径
- 证书更新时自动生效

## 证书管理脚本

### 1. 证书管理脚本 (`certificate_manager.sh`)

**功能**:
- 检查证书有效期
- 申请和续期证书
- 部署证书符号链接
- 重载相关服务

**关键特性**:
- 智能续期 (14天内到期才触发)
- 优先使用ECC证书
- 原子化符号链接更新
- 自动备份证书

**使用示例**:
```bash
# 检查证书状态
./scripts/certificate_manager.sh --check-expiry

# 强制续期证书
./scripts/certificate_manager.sh --force-renew

# 仅检查不执行操作
./scripts/certificate_manager.sh --check-only
```

### 2. 证书链接初始化脚本 (`init_certificate_links.sh`)

**功能**:
- 初始化证书符号链接结构
- 验证符号链接有效性
- 清理无效的符号链接
- 显示证书链接状态

**使用示例**:
```bash
# 初始化符号链接
./scripts/init_certificate_links.sh init

# 查看链接状态
./scripts/init_certificate_links.sh status

# 清理无效链接
./scripts/init_certificate_links.sh cleanup
```

## 证书生命周期管理

### 1. 初始申请

```bash
# 1. 设置环境变量
export CF_Token="your_cloudflare_api_token"
export CF_Zone_ID="your_cloudflare_zone_id"

# 2. 申请证书
acme.sh --issue --dns dns_cf -d matrix.example.com --keylength ec-256

# 3. 初始化符号链接
./scripts/init_certificate_links.sh init
```

### 2. 自动续期

证书管理脚本会自动检查证书有效期：
- 有效期 > 14天: 不执行任何操作
- 有效期 ≤ 14天: 自动续期并更新符号链接
- 证书不存在: 申请新证书

### 3. 手动续期

```bash
# 强制续期
./scripts/certificate_manager.sh --force-renew

# 验证续期结果
./scripts/init_certificate_links.sh status
```

## 部署弹性设计

### 重新部署保护

当重新部署项目时：

1. **保护原始证书**: `/root/.acme.sh/` 目录不受影响
2. **重建符号链接**: 部署脚本会重新创建所有符号链接
3. **服务连续性**: 证书更新不中断服务

### 故障恢复

如果符号链接损坏：

```bash
# 1. 清理无效链接
./scripts/init_certificate_links.sh cleanup

# 2. 重新初始化
./scripts/init_certificate_links.sh init

# 3. 重启服务
docker compose restart nginx coturn
```

## 安全考虑

### 文件权限

```bash
# acme.sh主存储
/root/.acme.sh/                    # 700 (仅root)
├── matrix.example.com_ecc/        # 700
│   ├── fullchain.cer              # 600
│   └── matrix.example.com.key     # 600

# 部署目录
/opt/matrix/data/acme/             # 755 (可读)
├── matrix.example.com/            # 755
│   ├── fullchain.cer              # 符号链接
│   └── private.key                # 符号链接

# 服务目录
/opt/matrix/data/nginx/certs/      # 755
/opt/matrix/data/coturn/certs/     # 755
```

### 访问控制

- **acme.sh目录**: 仅root用户可访问
- **部署目录**: 项目用户可读
- **Docker容器**: 通过只读挂载访问证书

## 监控和告警

### 证书监控

```bash
# 检查证书有效期
openssl x509 -in /opt/matrix/data/nginx/certs/fullchain.cer -noout -dates

# 检查符号链接状态
./scripts/init_certificate_links.sh status

# 验证证书链
openssl verify -CAfile /opt/matrix/data/nginx/certs/fullchain.cer /opt/matrix/data/nginx/certs/fullchain.cer
```

### 自动化监控

通过cron定时任务：

```bash
# 每天检查证书状态
0 2 * * * /opt/matrix/scripts/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1

# 每周验证符号链接
0 3 * * 0 /opt/matrix/scripts/init_certificate_links.sh status >> /var/log/matrix/certificate_links.log 2>&1
```

## 故障排除

### 常见问题

#### 1. 符号链接损坏

**症状**: 服务无法启动，SSL错误
**解决方案**:
```bash
./scripts/init_certificate_links.sh cleanup
./scripts/init_certificate_links.sh init
```

#### 2. 证书过期

**症状**: 浏览器显示证书错误
**解决方案**:
```bash
./scripts/certificate_manager.sh --force-renew
```

#### 3. acme.sh目录权限问题

**症状**: 证书申请失败
**解决方案**:
```bash
sudo chown -R root:root /root/.acme.sh
sudo chmod -R 700 /root/.acme.sh
```

#### 4. Docker容器无法访问证书

**症状**: Nginx或Coturn启动失败
**解决方案**:
```bash
# 检查Docker挂载
docker compose config

# 重新创建符号链接
./scripts/init_certificate_links.sh init

# 重启容器
docker compose restart nginx coturn
```

## 最佳实践

1. **定期备份**: 定期备份 `/root/.acme.sh/` 目录
2. **监控有效期**: 设置证书到期告警
3. **测试续期**: 定期测试证书续期流程
4. **验证链接**: 定期验证符号链接的有效性
5. **权限检查**: 确保文件权限正确设置

通过这种分层的证书管理策略，Matrix Homeserver项目实现了证书管理的自动化、弹性和安全性，确保了服务的长期稳定运行。
