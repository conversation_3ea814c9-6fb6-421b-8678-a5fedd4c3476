# Matrix Homeserver 项目结构说明 (修正版)

## 概述

本文档详细说明Matrix Homeserver项目的目录结构和文件组织原则，基于RouterOS API官方规范和Debian 12环境要求，确保开发人员能够正确理解和维护项目。

## 重要修正说明

**本项目已根据官方最新资料进行全面修正**:
- ✅ RouterOS API: 基于官方`routeros-api` 0.21.0库 (2025年3月7日发布)
- ✅ Debian 12: 遵循Python脚本和包管理要求
- ✅ 项目结构: internal/external目录仅包含部署必要文件
- ✅ 工具分离: 开发工具移至tools/目录

## 修正后的目录结构

```
synapse-matrix/
├── internal/                    # 内部部署包 (仅必要文件)
│   ├── docker-compose.yml      # Docker Compose配置
│   ├── config/                 # 配置文件
│   │   └── deployment.env.template
│   └── scripts/                # 部署脚本
│       ├── setup.sh            # 初始化部署脚本
│       ├── ip_watchdog.sh      # IP监控脚本 (支持RouterOS)
│       ├── certificate_manager.sh
│       ├── init_certificate_links.sh
│       ├── health_check.sh
│       ├── admin.sh
│       ├── backup.sh
│       └── utils/              # 工具函数
│           ├── common.sh
│           ├── logging.sh
│           ├── cloudflare_api.sh
│           └── routeros_client.py  # RouterOS客户端 (简化版)
├── external/                   # 外部部署包 (仅必要文件)
│   ├── config/
│   └── scripts/
├── tools/                      # 开发和测试工具 (新增)
│   ├── routeros_client.py      # RouterOS客户端 (完整版)
│   ├── install_routeros_deps.sh # 依赖安装脚本
│   ├── validate_config.py      # 配置验证工具
│   └── test_routeros_connection.sh # 连接测试脚本
├── docs/                       # 文档
│   ├── RouterOS_API_需求规格书.md  # 新增
│   ├── RouterOS配置指南.md      # 更新
│   ├── 项目结构说明_修正版.md   # 本文档
│   ├── 部署指南.md
│   └── 脚本使用指南.md
└── tests/                      # 测试文件 (预留)
    └── test_routeros_api.py
```

## 主要修正内容

### 1. RouterOS API 集成修正

#### 1.1 移除错误实现
- ❌ 删除了自制的RouterOS API协议实现
- ❌ 删除了`internal/scripts/utils/routeros_api.sh`
- ❌ 删除了`internal/scripts/routeros_setup.sh`

#### 1.2 采用官方实现
- ✅ 使用官方`routeros-api`库 (版本0.21.0)
- ✅ 正确的导入方式: `import routeros_api`
- ✅ 正确的连接参数: `plaintext_login=True` (RouterOS 6.43+)

#### 1.3 双层实现架构
- **简化版** (`internal/scripts/utils/routeros_client.py`): 仅IP获取功能，用于部署
- **完整版** (`tools/routeros_client.py`): 完整功能，用于开发和测试

### 2. Debian 12 兼容性修正

#### 2.1 Python脚本规范
- ✅ Shebang: `#!/usr/bin/env python3`
- ✅ 可执行权限: `chmod +x script.py`
- ✅ 外部管理环境: `--break-system-packages`选项

#### 2.2 依赖管理策略
```bash
# 系统级安装 (推荐)
pip3 install --break-system-packages routeros-api==0.21.0

# 用户级安装 (备选)
pip3 install --user routeros-api==0.21.0
```

### 3. 项目结构优化

#### 3.1 文件分类原则
- **internal/external**: 仅部署必需文件
- **tools/**: 开发、测试、配置工具
- **docs/**: 完整文档
- **tests/**: 测试文件

#### 3.2 RouterOS工具分布
| 文件 | 位置 | 功能 | 用途 |
|------|------|------|------|
| `routeros_client.py` | `internal/scripts/utils/` | IP获取 | 部署运行 |
| `routeros_client.py` | `tools/` | 完整功能 | 开发测试 |
| `install_routeros_deps.sh` | `tools/` | 依赖安装 | 环境准备 |
| `validate_config.py` | `tools/` | 配置验证 | 配置检查 |

#### 3.3 证书管理架构
本项目采用三层符号链接架构，避免重复申请证书：

| 层级 | 路径 | 说明 |
|------|------|------|
| **源存储层** | `/root/.acme.sh/matrix.example.com_ecc/` | acme.sh默认位置，ECC证书优先 |
| **源存储层** | `/root/.acme.sh/matrix.example.com/` | acme.sh默认位置，RSA证书备选 |
| **中间层** | `/opt/matrix/data/acme/matrix.example.com/` | 部署目录，符号链接到源存储 |
| **服务层** | `/opt/matrix/data/nginx/certs/` | Nginx服务，符号链接到中间层 |
| **服务层** | `/opt/matrix/data/coturn/certs/` | Coturn服务，符号链接到中间层 |

**优势**:
- ✅ **避免重复申请**: 使用acme.sh默认存储位置
- ✅ **ECC/RSA兼容**: 优先ECC，自动兼容RSA证书
- ✅ **原子化更新**: 证书更新过程中服务不中断
- ✅ **故障恢复**: 符号链接损坏时自动修复

## 使用指南

### 1. 开发环境设置

```bash
# 1. 安装RouterOS API依赖
./tools/install_routeros_deps.sh

# 2. 验证安装
python3 -c "import routeros_api; print('版本:', routeros_api.__version__)"

# 3. 配置RouterOS参数
cp internal/config/deployment.env.template internal/config/deployment.env
nano internal/config/deployment.env
```

### 2. RouterOS配置验证

```bash
# 验证配置文件
python3 tools/validate_config.py --config-file internal/config/deployment.env

# 测试连接
python3 tools/routeros_client.py --host *********** --user admin --password password test

# 获取WAN IP
python3 tools/routeros_client.py --host *********** --user admin --password password get-wan-ip
```

### 3. 部署流程

```bash
# 1. 进入内部部署目录
cd internal/

# 2. 运行初始化脚本 (会自动检查RouterOS依赖)
./scripts/setup.sh

# 3. 测试IP监控 (会自动使用RouterOS如果配置了)
./scripts/ip_watchdog.sh --check-only

# 4. 健康检查 (包含RouterOS状态)
./scripts/health_check.sh --detailed
```

## 技术规范

### 1. RouterOS API 规范

#### 1.1 官方库信息
- **包名**: `routeros-api`
- **版本**: 0.21.0 (2025年3月7日)
- **维护者**: Social WiFi
- **许可证**: MIT License
- **支持Python**: 3.9, 3.10, 3.11, 3.12

#### 1.2 连接参数
```python
routeros_api.RouterOsApiPool(
    host='***********',        # RouterOS设备IP
    username='admin',          # 用户名
    password='password',       # 密码
    port=8728,                # API端口
    plaintext_login=True,     # RouterOS 6.43+必需
    use_ssl=False,            # 是否使用SSL
    ssl_verify=False,         # SSL证书验证
    ssl_verify_hostname=False # SSL主机名验证
)
```

### 2. 集成策略

#### 2.1 IP监控集成
```bash
# ip_watchdog.sh 执行流程
1. 检查RouterOS配置 (ROUTEROS_HOST, ROUTEROS_USER)
2. 检查routeros-api库是否安装
3. 调用简化版RouterOS客户端获取IP
4. 失败时降级到外部IP查询服务
5. 更新DNS和Coturn配置
```

#### 2.2 健康检查集成
```bash
# health_check.sh 检查项目
1. RouterOS API库安装状态
2. RouterOS连接状态
3. WAN接口IP获取状态
4. 整体健康评估
```

## 开发规范

### 1. 代码质量要求

- **错误处理**: 完善的异常处理和降级策略
- **日志记录**: 适当的日志级别 (简化版静默，完整版详细)
- **参数验证**: 严格的输入验证和IP格式检查
- **超时控制**: 合理的网络超时设置

### 2. 兼容性要求

- **系统兼容**: 严格遵循Debian 12要求
- **版本兼容**: 明确指定依赖版本号
- **降级兼容**: RouterOS不可用时的优雅降级
- **配置兼容**: 向后兼容的配置格式

### 3. 安全要求

- **权限最小化**: 使用专用API用户
- **密码安全**: 避免明文密码传递
- **网络安全**: 内网通信，限制访问来源
- **错误安全**: 避免敏感信息泄露

## 故障排除

### 1. 常见问题

#### 1.1 RouterOS API库安装失败
```bash
# 问题: pip install失败
# 解决: 使用Debian 12兼容选项
pip3 install --break-system-packages routeros-api==0.21.0
```

#### 1.2 RouterOS连接失败
```bash
# 问题: 连接被拒绝
# 解决: 检查API服务状态
/ip service print where name=api
/ip service enable api
```

#### 1.3 权限不足
```bash
# 问题: 认证失败
# 解决: 检查用户权限
/user print where name=matrix-api
/user set matrix-api group=full
```

### 2. 调试工具

```bash
# 详细连接测试
python3 tools/routeros_client.py --host *********** --user admin --password password --verbose test

# 配置验证
python3 tools/validate_config.py --config-file internal/config/deployment.env

# 网络连通性测试
tools/test_routeros_connection.sh *********** admin password
```

## 维护指南

### 1. 定期维护

- **依赖更新**: 定期检查routeros-api库更新
- **配置检查**: 定期验证RouterOS配置
- **连接测试**: 定期测试RouterOS连接
- **文档同步**: 保持文档与代码同步

### 2. 监控建议

- **API可用性**: 监控RouterOS API服务状态
- **连接成功率**: 监控IP获取成功率
- **降级频率**: 监控外部服务使用频率
- **错误日志**: 监控RouterOS相关错误

通过遵循这些修正后的规范和最佳实践，可以确保Matrix Homeserver项目的RouterOS API集成符合官方标准，并在Debian 12环境下稳定运行。
