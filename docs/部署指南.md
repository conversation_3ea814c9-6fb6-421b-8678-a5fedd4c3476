# Matrix Homeserver 完整部署指南

## 概述

本指南详细说明如何部署高可用性分离式Matrix Homeserver系统。系统由两个独立的部署包组成：
- **内部核心服务 (Internal Server)**: 运行在动态IP的内网服务器上
- **外部指路牌服务 (External Server)**: 运行在静态IP的VPS上

## 部署架构

```
[客户端/联邦服务器]
    ↓ 查询主域名
[外部指路牌 VPS - 静态IP:80/443]
    ↓ 返回.well-known指引
[内部核心服务 - 动态IP:自定义端口]
    ↓ 实际Matrix服务
[Docker容器集群]
```

## 前置准备

### 1. 硬件要求

#### 外部指路牌 VPS
- **CPU**: 1核心
- **内存**: 1GB
- **存储**: 20GB SSD
- **网络**: 静态公网IP，80/443端口

#### 内部核心服务器
- **CPU**: 4核心（推荐8核心）
- **内存**: 8GB（推荐16GB）
- **存储**: 100GB SSD（推荐200GB以上）
- **网络**: 动态公网IP，支持端口转发

### 2. 域名和DNS配置

#### 域名要求
- 主域名：`example.com`
- Matrix子域名：`matrix.example.com`

#### DNS配置
```
# 主域名A记录指向外部VPS静态IP
example.com.        A    ************

# Matrix子域名A记录指向内部服务器动态IP (由脚本自动更新)
matrix.example.com. A    *************
```

#### Cloudflare配置
1. 注册Cloudflare账户并添加域名
2. 获取API Token (需要Zone:Edit权限)
3. 获取Zone ID

### 3. 网络配置

#### 路由器端口转发
在内网路由器中配置以下端口转发规则：

| 协议 | 外部端口 | 内部端口 | 内部IP | 服务 |
|------|----------|----------|--------|------|
| TCP | 8443 | 8443 | ************* | Matrix HTTPS |
| TCP | 5349 | 5349 | ************* | TURN TLS |
| TCP | 3478 | 3478 | ************* | TURN/STUN |
| UDP | 3478 | 3478 | ************* | TURN/STUN |
| UDP | 49152-65535 | 49152-65535 | ************* | TURN媒体 |

## 部署步骤

### 第一阶段：部署外部指路牌服务

#### 1. 准备VPS环境
```bash
# 连接到VPS
ssh root@your-vps-ip

# 更新系统
apt update && apt upgrade -y

# 创建部署用户
useradd -m -s /bin/bash matrix
usermod -aG sudo matrix
su - matrix
```

#### 2. 下载部署包
```bash
# 下载项目
git clone <repository-url>
cd synapse-matrix

# 复制外部服务部署包
cp -r external /opt/matrix-signpost
cd /opt/matrix-signpost
```

#### 3. 配置外部服务
```bash
# 复制配置模板
cp config/signpost.env.template config/signpost.env

# 编辑配置文件
nano config/signpost.env
```

配置示例：
```bash
DOMAIN="example.com"
SUBDOMAIN_MATRIX="matrix"
HTTPS_PORT="8443"
ADMIN_EMAIL="<EMAIL>"
```

#### 4. 执行部署
```bash
# 运行部署脚本
sudo ./scripts/deploy.sh

# 验证部署
curl https://example.com/.well-known/matrix/client
curl https://example.com/.well-known/matrix/server
```

### 第二阶段：部署内部核心服务

#### 1. 准备内网服务器
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装acme.sh
curl https://get.acme.sh | sh
source ~/.bashrc

# 重新登录以应用Docker组权限
```

#### 2. 下载部署包
```bash
# 下载项目
git clone <repository-url>
cd synapse-matrix

# 复制内部服务部署包
sudo cp -r internal /opt/matrix
sudo chown -R $USER:$USER /opt/matrix
cd /opt/matrix
```

#### 3. 配置内部服务
```bash
# 复制配置模板
cp config/deployment.env.template config/deployment.env

# 编辑配置文件
nano config/deployment.env
```

配置示例：
```bash
# 基础域名配置
DOMAIN="example.com"
SUBDOMAIN_MATRIX="matrix"
HTTPS_PORT="8443"

# Cloudflare DNS配置
CLOUDFLARE_API_TOKEN="your_api_token_here"
CLOUDFLARE_ZONE_ID="your_zone_id_here"

# 安全配置 (生成随机密码)
DB_PASSWORD="$(openssl rand -base64 32)"
COTURN_SHARED_SECRET="$(openssl rand -base64 32)"

# 部署路径
DEPLOY_DIR="/opt/matrix"
```

#### 4. 执行部署
```bash
# 设置脚本权限
chmod +x scripts/*.sh

# 运行初始化脚本
./scripts/setup.sh

# 检查服务状态
docker compose ps
```

### 第三阶段：验证和测试

#### 1. 服务健康检查
```bash
# 检查内部服务
cd /opt/matrix
./scripts/health_check.sh

# 检查外部服务
curl https://example.com/health
```

#### 2. 创建管理员用户
```bash
# 在内部服务器上执行
cd /opt/matrix
docker compose exec synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008
```

#### 3. 测试Matrix功能
```bash
# 测试客户端API
curl -k https://matrix.example.com:8443/_matrix/client/versions

# 测试联邦API
curl -k https://matrix.example.com:8443/_matrix/federation/v1/version

# 测试.well-known发现
curl https://example.com/.well-known/matrix/client
curl https://example.com/.well-known/matrix/server
```

#### 4. 客户端连接测试
1. 打开Element Web客户端：https://app.element.io
2. 选择"自定义服务器"
3. 输入服务器地址：`example.com`
4. 使用创建的管理员账户登录

## 运维管理

### 自动化任务

#### 内部服务定时任务
```bash
# 查看已配置的定时任务
crontab -l

# 手动执行脚本
./scripts/ip_watchdog.sh --check-only
./scripts/certificate_manager.sh --check-expiry
./scripts/health_check.sh
./scripts/backup.sh
```

#### 外部服务定时任务
```bash
# 证书自动续期已在部署时配置
sudo certbot renew --dry-run

# 查看证书状态
sudo certbot certificates
```

### 监控和日志

#### 内部服务监控
```bash
# 查看服务状态
docker compose ps

# 查看服务日志
docker compose logs -f synapse
docker compose logs -f nginx
docker compose logs -f coturn

# 查看脚本日志
tail -f /var/log/matrix/ip_watchdog.log
tail -f /var/log/matrix/certificate_manager.log
```

#### 外部服务监控
```bash
# 查看Nginx状态
sudo systemctl status nginx

# 查看访问日志
sudo tail -f /var/log/nginx/access.log

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 备份和恢复

#### 内部服务备份
```bash
# 手动备份
./scripts/backup.sh

# 查看备份文件
ls -la data/backup/
```

#### 外部服务备份
```bash
# 备份配置和证书
sudo tar -czf /backup/signpost-$(date +%Y%m%d).tar.gz /etc/nginx/ /etc/letsencrypt/ /var/www/html/
```

## 故障排除

### 常见问题

#### 1. IP变更后服务不可达
```bash
# 检查当前IP
curl -s https://ipv4.icanhazip.com/

# 手动更新IP
./scripts/ip_watchdog.sh --force-update

# 检查DNS解析
nslookup matrix.example.com
```

#### 2. 证书过期
```bash
# 检查证书状态
./scripts/certificate_manager.sh --check-expiry

# 手动续期
./scripts/certificate_manager.sh --force-renew
```

#### 3. 服务启动失败
```bash
# 检查Docker状态
sudo systemctl status docker

# 检查配置文件
./scripts/validate_config.sh

# 查看详细错误
docker compose logs
```

#### 4. 联邦通信问题
```bash
# 检查.well-known文件
curl https://example.com/.well-known/matrix/server

# 检查端口转发
sudo netstat -tlnp | grep :8443

# 测试外部访问
curl -k https://matrix.example.com:8443/_matrix/federation/v1/version
```

## 安全建议

### 系统安全
1. **定期更新**: 保持系统和软件包最新
2. **防火墙**: 仅开放必要端口
3. **SSH安全**: 使用密钥认证，禁用密码登录
4. **用户权限**: 遵循最小权限原则

### 应用安全
1. **强密码**: 使用复杂的数据库和服务密码
2. **证书管理**: 确保SSL证书自动续期正常工作
3. **访问控制**: 限制管理接口访问
4. **日志监控**: 定期检查系统和应用日志

### 网络安全
1. **端口限制**: 仅开放必要的服务端口
2. **速率限制**: 配置适当的API速率限制
3. **DDoS防护**: 考虑使用Cloudflare等CDN服务
4. **监控告警**: 设置异常访问告警

## 性能优化

### 数据库优化
- 调整PostgreSQL配置参数
- 定期执行数据库维护
- 监控数据库性能指标

### 缓存优化
- 调整Redis内存配置
- 优化缓存策略
- 监控缓存命中率

### 网络优化
- 启用HTTP/2和gzip压缩
- 配置适当的缓存头
- 使用CDN加速静态资源

通过遵循本部署指南，您可以成功部署一个高可用、安全、可扩展的Matrix Homeserver系统。如有问题，请参考故障排除部分或查看详细的技术文档。
