# Coturn TURN/STUN服务器配置
# 此文件由脚本自动生成，请勿手动编辑

# 基础配置
listening-port=3478
tls-listening-port=5349
listening-ip=0.0.0.0

# 外部IP配置 (由脚本动态更新)
external-ip=PLACEHOLDER_EXTERNAL_IP

# 中继IP配置
relay-ip=0.0.0.0

# 端口范围配置 (由脚本动态更新)
min-port=PLACEHOLDER_MIN_PORT
max-port=PLACEHOLDER_MAX_PORT

# 域名配置
realm=${DOMAIN}
server-name=${SUBDOMAIN_MATRIX}.${DOMAIN}

# 认证配置
use-auth-secret
static-auth-secret=${COTURN_SHARED_SECRET}

# 证书配置 (使用软链接)
cert=/etc/coturn/certs/fullchain.cer
pkey=/etc/coturn/certs/private.key

# 协议配置
fingerprint
lt-cred-mech

# 安全配置
no-stdout-log
log-file=/var/log/coturn/coturn.log
pidfile=/var/run/coturn/coturn.pid

# 禁用不安全的协议
no-tlsv1
no-tlsv1_1

# 性能配置
total-quota=100
bps-capacity=0
stale-nonce=600

# 网络配置
no-multicast-peers
no-cli
no-loopback-peers
no-tcp-relay

# 数据库配置 (使用内存数据库)
userdb=/var/lib/coturn/turndb

# 详细日志 (生产环境可关闭)
verbose

# 允许的对等地址
denied-peer-ip=10.0.0.0-**************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=*********-***************

# 允许的源地址
allowed-peer-ip=0.0.0.0-***************

# 禁用TCP中继 (仅使用UDP)
no-tcp-relay

# 启用STUN绑定请求
stun-only

# 移动性支持
mobility

# 软件名称
prod
