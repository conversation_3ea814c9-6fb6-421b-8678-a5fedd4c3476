# Matrix Synapse Homeserver配置
# 此文件由脚本自动生成，请勿手动编辑

# 服务器配置
server_name: "${DOMAIN}"
pid_file: /data/homeserver.pid
web_client_location: https://app.element.io/
public_baseurl: https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}/

# 监听配置
listeners:
  - port: 8008
    tls: false
    type: http
    x_forwarded: true
    bind_addresses: ['0.0.0.0']
    resources:
      - names: [client, federation]
        compress: false

# 数据库配置
database:
  name: psycopg2
  args:
    user: ${DB_USER}
    password: ${DB_PASSWORD}
    database: ${DB_NAME}
    host: ${DB_HOST}
    port: ${DB_PORT}
    cp_min: 5
    cp_max: 10

# Redis配置
redis:
  enabled: true
  host: ${REDIS_HOST}
  port: ${REDIS_PORT}
  # password: ${REDIS_PASSWORD}  # 如果Redis设置了密码

# 日志配置
log_config: "/data/log.config"

# 媒体存储配置
media_store_path: "/data/media_store"
max_upload_size: ${MAX_UPLOAD_SIZE:-50M}
max_image_pixels: 32M
dynamic_thumbnails: false

# URL预览配置
url_preview_enabled: false
url_preview_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# 注册配置
enable_registration: ${ENABLE_REGISTRATION:-false}
enable_registration_without_verification: false
registration_shared_secret: "${SYNAPSE_REGISTRATION_SECRET}"

# 访客访问配置
allow_guest_access: ${ENABLE_GUEST_ACCESS:-false}

# 隐私配置 - 禁用第三方身份服务器
enable_3pid_lookup: false
disable_msisdn_registration: true
account_threepid_delegates:
  # 完全移除身份服务器配置

# 联邦配置
federation_domain_whitelist: []  # 空列表表示允许所有域名
federation_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# TURN服务器配置
turn_uris:
  - "turn:${SUBDOMAIN_MATRIX}.${DOMAIN}:${TURN_PORT}?transport=udp"
  - "turn:${SUBDOMAIN_MATRIX}.${DOMAIN}:${TURN_PORT}?transport=tcp"
  - "turns:${SUBDOMAIN_MATRIX}.${DOMAIN}:${TURNS_PORT}?transport=tcp"

turn_shared_secret: "${COTURN_SHARED_SECRET}"
turn_user_lifetime: 1h
turn_allow_guests: true

# 安全配置
bcrypt_rounds: 12
form_secret: "${SYNAPSE_FORM_SECRET}"
macaroon_secret_key: "${SYNAPSE_MACAROON_SECRET}"

# 速率限制
rc_message:
  per_second: 0.2
  burst_count: 10

rc_registration:
  per_second: 0.17
  burst_count: 3

rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

rc_admin_redaction:
  per_second: 1
  burst_count: 50

rc_joins:
  local:
    per_second: 0.1
    burst_count: 3
  remote:
    per_second: 0.01
    burst_count: 3

# 房间配置
encryption_enabled_by_default_for_room_type: all
enable_room_list_search: false

# 用户目录配置
user_directory:
  enabled: true
  search_all_users: false
  prefer_local_users: true

# 统计报告配置
report_stats: false
enable_metrics: false

# 缓存配置
caches:
  global_factor: 0.5
  per_cache_factors:
    get_users_who_share_room_with_user: 2.0

# 应用服务配置
app_service_config_files: []

# 推送配置
push:
  include_content: true
  group_unread_count_by_room: false

# 实验性功能
experimental_features:
  spaces_enabled: true
  msc3026_enabled: true

# 签名密钥文件
signing_key_path: "/data/signing.key"

# 信任的密钥服务器
trusted_key_servers:
  - server_name: "matrix.org"

# 抑制密钥服务器警告
suppress_key_server_warning: true

# 模块配置
modules: []

# 工作进程配置 (单进程模式)
worker_app: synapse.app.homeserver
worker_log_config: /data/log.config
