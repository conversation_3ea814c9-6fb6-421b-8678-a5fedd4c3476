# ================================================================
# Matrix Homeserver 内部服务部署配置
# ================================================================
# 复制此文件为 deployment.env 并根据实际环境修改配置值
# 注意：请勿将包含敏感信息的 deployment.env 文件提交到版本控制系统

# ================================================================
# 基础域名配置
# ================================================================

# 主域名 (用于Matrix服务器名称)
DOMAIN="example.com"

# Matrix服务子域名 (用于实际服务访问)
SUBDOMAIN_MATRIX="matrix"

# 完整的Matrix服务域名 (自动生成，无需修改)
MATRIX_DOMAIN="${SUBDOMAIN_MATRIX}.${DOMAIN}"

# ================================================================
# 网络端口配置
# ================================================================

# HTTPS服务端口 (由于ISP封禁443，使用自定义端口)
HTTPS_PORT="8443"

# Coturn TURN/STUN端口配置
TURN_PORT="3478"
TURNS_PORT="5349"

# Coturn媒体转发端口范围
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"

# ================================================================
# 部署路径配置
# ================================================================

# 主部署目录 (所有服务数据和配置的根目录)
DEPLOY_DIR="/opt/matrix"

# 数据目录
DATA_DIR="${DEPLOY_DIR}/data"

# 脚本目录
SCRIPTS_DIR="${DEPLOY_DIR}/scripts"

# 配置模板目录
CONFIG_DIR="${DEPLOY_DIR}/config"

# 日志目录
LOG_DIR="/var/log/matrix"

# ================================================================
# 数据库配置
# ================================================================

# PostgreSQL数据库配置
DB_NAME="synapse"
DB_USER="synapse"
DB_PASSWORD="CHANGE_ME_TO_SECURE_PASSWORD"
DB_HOST="db"
DB_PORT="5432"

# Redis配置
REDIS_HOST="redis"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# ================================================================
# Cloudflare DNS API 配置
# ================================================================

# Cloudflare API Token (需要Zone:Edit权限)
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token_here"

# Cloudflare Zone ID
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id_here"

# DNS记录TTL (秒)
DNS_TTL="300"

# ================================================================
# 证书管理配置
# ================================================================

# acme.sh配置 (使用默认位置作为主存储)
ACME_HOME="/root/.acme.sh"
ACME_EMAIL="admin@${DOMAIN}"

# 证书续期阈值 (天数)
CERT_RENEWAL_THRESHOLD="14"

# 证书符号链接目录 (部署目录中的链接)
ACME_DEPLOY_DIR="${DATA_DIR}/acme"
NGINX_CERT_DIR="${DATA_DIR}/nginx/certs"
COTURN_CERT_DIR="${DATA_DIR}/coturn/certs"

# ================================================================
# Synapse配置
# ================================================================

# 服务器名称 (必须与主域名一致)
SYNAPSE_SERVER_NAME="${DOMAIN}"

# 管理员用户
SYNAPSE_ADMIN_USER="admin"

# 注册配置
ENABLE_REGISTRATION="false"
ENABLE_GUEST_ACCESS="false"

# 媒体存储配置
MAX_UPLOAD_SIZE="50M"
MEDIA_STORE_PATH="${DATA_DIR}/synapse/media"

# ================================================================
# 安全配置
# ================================================================

# 生成随机密钥的命令示例：
# openssl rand -base64 32

# Synapse签名密钥 (首次部署时自动生成)
SYNAPSE_SIGNING_KEY=""

# Coturn共享密钥
COTURN_SHARED_SECRET="CHANGE_ME_TO_SECURE_SECRET"

# ================================================================
# 性能调优配置
# ================================================================

# Docker容器资源限制
SYNAPSE_MEMORY_LIMIT="2g"
SYNAPSE_CPU_LIMIT="2"

POSTGRES_MEMORY_LIMIT="1g"
POSTGRES_CPU_LIMIT="1"

REDIS_MEMORY_LIMIT="512m"
REDIS_CPU_LIMIT="0.5"

NGINX_MEMORY_LIMIT="256m"
NGINX_CPU_LIMIT="0.5"

COTURN_MEMORY_LIMIT="512m"
COTURN_CPU_LIMIT="1"

# PostgreSQL调优参数
POSTGRES_SHARED_BUFFERS="256MB"
POSTGRES_EFFECTIVE_CACHE_SIZE="1GB"
POSTGRES_WORK_MEM="4MB"
POSTGRES_MAINTENANCE_WORK_MEM="64MB"

# ================================================================
# 监控与日志配置
# ================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL="INFO"

# 日志轮转配置
LOG_ROTATE_DAYS="30"
LOG_COMPRESS="true"

# 监控告警配置
ALERT_EMAIL="admin@${DOMAIN}"
WEBHOOK_URL=""

# ================================================================
# 自动化脚本配置
# ================================================================

# IP监控脚本执行间隔 (分钟)
IP_CHECK_INTERVAL="1"

# 证书检查脚本执行时间 (cron格式)
CERT_CHECK_SCHEDULE="0 2 * * *"

# 脚本超时时间 (秒)
SCRIPT_TIMEOUT="300"

# 重试次数
MAX_RETRIES="3"

# ================================================================
# 环境标识
# ================================================================

# 部署环境 (development, staging, production)
ENVIRONMENT="production"

# 部署版本
DEPLOYMENT_VERSION="1.0.0"

# 最后更新时间
LAST_UPDATED="$(date -u +%Y-%m-%dT%H:%M:%SZ)"

# ================================================================
# 配置验证
# ================================================================

# 必需配置项检查 (由脚本使用)
REQUIRED_VARS=(
    "DOMAIN"
    "SUBDOMAIN_MATRIX"
    "HTTPS_PORT"
    "DB_PASSWORD"
    "CLOUDFLARE_API_TOKEN"
    "CLOUDFLARE_ZONE_ID"
    "COTURN_SHARED_SECRET"
)

# 敏感配置项 (不应出现在日志中)
SENSITIVE_VARS=(
    "DB_PASSWORD"
    "CLOUDFLARE_API_TOKEN"
    "COTURN_SHARED_SECRET"
    "SYNAPSE_SIGNING_KEY"
)

# ================================================================
# 使用说明
# ================================================================

# 1. 复制此模板文件：
#    cp config/deployment.env.template config/deployment.env
#
# 2. 编辑配置文件：
#    nano config/deployment.env
#
# 3. 生成安全密钥：
#    DB_PASSWORD=$(openssl rand -base64 32)
#    COTURN_SHARED_SECRET=$(openssl rand -base64 32)
#
# 4. 设置正确的文件权限：
#    chmod 600 config/deployment.env
#
# 5. 验证配置：
#    ./scripts/validate_config.sh
#
# 6. 开始部署：
#    ./scripts/setup.sh
