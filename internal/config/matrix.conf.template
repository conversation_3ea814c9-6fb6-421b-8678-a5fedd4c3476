# Matrix Homeserver 站点配置
# 此文件由脚本自动生成，请勿手动编辑

server {
    listen ${HTTPS_PORT} ssl http2;
    server_name ${SUBDOMAIN_MATRIX}.${DOMAIN};

    # SSL证书配置 (使用软链接)
    ssl_certificate /etc/nginx/certs/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/private.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 客户端配置
    client_max_body_size ${MAX_UPLOAD_SIZE:-50M};

    # Matrix客户端API
    location ~ ^(/_matrix|/_synapse/client) {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲配置
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # Matrix联邦API
    location ~ ^(/_matrix/federation) {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 媒体文件直接服务 (可选，提高性能)
    location ~ ^/_matrix/media/r0/download/(.+)/(.+) {
        alias /var/www/matrix/media/local_content/$1/$2;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Access-Control-Allow-Origin "*";
        
        # 如果文件不存在，回退到Synapse
        try_files $uri @synapse_media;
    }

    location ~ ^/_matrix/media/r0/thumbnail/(.+)/(.+) {
        alias /var/www/matrix/media/local_thumbnails/$1/$2;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Access-Control-Allow-Origin "*";
        
        # 如果文件不存在，回退到Synapse
        try_files $uri @synapse_media;
    }

    # 媒体文件回退处理
    location @synapse_media {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 健康检查端点
    location /_matrix/client/versions {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        
        # 缓存健康检查响应
        proxy_cache_valid 200 1m;
        add_header Cache-Control "public, max-age=60";
    }

    # 管理API (限制访问)
    location ~ ^/_synapse/admin {
        # 仅允许本地访问
        allow 127.0.0.1;
        allow **********/16;  # Docker网络
        deny all;

        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态文件服务 (如果需要)
    location /static/ {
        alias /var/www/matrix/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 默认拒绝其他请求
    location / {
        return 404;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        return 404 "Matrix service not found";
    }
    
    location = /50x.html {
        internal;
        return 500 "Matrix service temporarily unavailable";
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name ${SUBDOMAIN_MATRIX}.${DOMAIN};
    
    # 重定向到HTTPS
    return 301 https://$server_name:${HTTPS_PORT}$request_uri;
}
