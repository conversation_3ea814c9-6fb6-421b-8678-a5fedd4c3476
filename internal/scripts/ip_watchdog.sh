#!/bin/bash

# ================================================================
# Matrix Homeserver IP监控脚本
# ================================================================
# 功能: 监控动态公网IP变化，自动更新DNS记录和Coturn配置
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载配置
if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
    source "$PROJECT_DIR/config/deployment.env"
else
    echo "错误: 配置文件 $PROJECT_DIR/config/deployment.env 不存在"
    exit 1
fi

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"
source "$SCRIPT_DIR/utils/cloudflare_api.sh"

# 默认配置
CURRENT_IP_FILE="${DATA_DIR}/last_ip.txt"
LOG_FILE="${LOG_DIR}/ip_watchdog.log"
LOCK_FILE="/tmp/ip_watchdog.lock"
MAX_RETRIES=${MAX_RETRIES:-3}
RETRY_DELAY=5

# 命令行参数
FORCE_UPDATE=false
CHECK_ONLY=false
VERBOSE=false

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-update)
                FORCE_UPDATE=true
                shift
                ;;
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver IP监控脚本

用法: $0 [选项]

选项:
    --force-update    强制更新IP，即使IP未变化
    --check-only      仅检查IP，不执行更新操作
    --verbose         显示详细输出
    --help            显示此帮助信息

示例:
    $0                      # 正常运行，检查并更新IP
    $0 --check-only         # 仅检查当前IP状态
    $0 --force-update       # 强制更新所有配置
    $0 --verbose            # 显示详细日志

EOF
}

# 创建锁文件
create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local lock_pid
        lock_pid=$(cat "$LOCK_FILE" 2>/dev/null || echo "")
        if [[ -n "$lock_pid" ]] && kill -0 "$lock_pid" 2>/dev/null; then
            log_error "脚本已在运行 (PID: $lock_pid)"
            exit 1
        else
            log_warn "发现过期的锁文件，正在清理"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# 清理锁文件
cleanup_lock() {
    rm -f "$LOCK_FILE"
}

# 获取当前公网IP
get_current_ip() {
    local ip=""
    local services=(
        "https://ipv4.icanhazip.com/"
        "https://api.ipify.org"
        "https://checkip.amazonaws.com/"
        "https://ipinfo.io/ip"
    )
    
    for service in "${services[@]}"; do
        if ip=$(curl -s --connect-timeout 10 --max-time 30 "$service" | tr -d '\n\r'); then
            if [[ "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
                echo "$ip"
                return 0
            fi
        fi
        log_warn "IP服务 $service 不可用，尝试下一个"
    done
    
    log_error "无法获取当前公网IP"
    return 1
}

# 读取上次记录的IP
get_last_ip() {
    if [[ -f "$CURRENT_IP_FILE" ]]; then
        cat "$CURRENT_IP_FILE" 2>/dev/null || echo ""
    else
        echo ""
    fi
}

# 保存当前IP
save_current_ip() {
    local ip="$1"
    echo "$ip" > "$CURRENT_IP_FILE"
    log_info "已保存当前IP: $ip"
}

# 更新Cloudflare DNS记录
update_dns_record() {
    local ip="$1"
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    
    log_info "正在更新DNS记录: $domain -> $ip"
    
    if cloudflare_update_record "$domain" "$ip"; then
        log_info "DNS记录更新成功"
        return 0
    else
        log_error "DNS记录更新失败"
        return 1
    fi
}

# 更新Coturn配置
update_coturn_config() {
    local ip="$1"
    local config_file="${DATA_DIR}/coturn/conf/turnserver.conf"
    local temp_file="${config_file}.tmp"
    
    log_info "正在更新Coturn配置: external-ip=$ip"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Coturn配置文件不存在: $config_file"
        return 1
    fi
    
    # 更新external-ip配置
    sed "s/^external-ip=.*/external-ip=$ip/" "$config_file" > "$temp_file"
    
    # 更新端口范围配置
    sed -i "s/^min-port=.*/min-port=${COTURN_MIN_PORT}/" "$temp_file"
    sed -i "s/^max-port=.*/max-port=${COTURN_MAX_PORT}/" "$temp_file"
    
    # 验证配置文件
    if grep -q "external-ip=$ip" "$temp_file"; then
        mv "$temp_file" "$config_file"
        log_info "Coturn配置更新成功"
        return 0
    else
        rm -f "$temp_file"
        log_error "Coturn配置更新失败"
        return 1
    fi
}

# 重启Coturn服务
restart_coturn() {
    log_info "正在重启Coturn服务"
    
    if docker compose -f "$PROJECT_DIR/docker-compose.yml" restart coturn; then
        log_info "Coturn服务重启成功"
        
        # 等待服务启动
        sleep 5
        
        # 验证服务状态
        if docker compose -f "$PROJECT_DIR/docker-compose.yml" ps coturn | grep -q "Up"; then
            log_info "Coturn服务运行正常"
            return 0
        else
            log_error "Coturn服务启动失败"
            return 1
        fi
    else
        log_error "Coturn服务重启失败"
        return 1
    fi
}

# 验证IP更新
verify_ip_update() {
    local expected_ip="$1"
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    
    log_info "正在验证IP更新: $domain"
    
    # 等待DNS传播
    sleep 10
    
    local resolved_ip
    if resolved_ip=$(nslookup "$domain" ******* | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null); then
        if [[ "$resolved_ip" == "$expected_ip" ]]; then
            log_info "DNS解析验证成功: $domain -> $resolved_ip"
            return 0
        else
            log_warn "DNS解析不匹配: 期望 $expected_ip，实际 $resolved_ip"
            return 1
        fi
    else
        log_warn "DNS解析验证失败"
        return 1
    fi
}

# 主要处理逻辑
main() {
    local current_ip last_ip
    
    log_info "开始IP监控检查"
    
    # 获取当前IP
    if ! current_ip=$(get_current_ip); then
        log_error "无法获取当前公网IP"
        exit 1
    fi
    
    log_info "当前公网IP: $current_ip"
    
    # 获取上次记录的IP
    last_ip=$(get_last_ip)
    log_info "上次记录IP: ${last_ip:-未记录}"
    
    # 检查是否需要更新
    if [[ "$CHECK_ONLY" == "true" ]]; then
        log_info "仅检查模式，不执行更新操作"
        if [[ "$current_ip" != "$last_ip" ]]; then
            log_info "检测到IP变化: $last_ip -> $current_ip"
        else
            log_info "IP未发生变化"
        fi
        exit 0
    fi
    
    # 判断是否需要更新
    if [[ "$FORCE_UPDATE" == "true" ]] || [[ "$current_ip" != "$last_ip" ]]; then
        if [[ "$FORCE_UPDATE" == "true" ]]; then
            log_info "强制更新模式"
        else
            log_info "检测到IP变化: $last_ip -> $current_ip"
        fi
        
        local retry_count=0
        local update_success=false
        
        while [[ $retry_count -lt $MAX_RETRIES ]]; do
            log_info "开始更新操作 (尝试 $((retry_count + 1))/$MAX_RETRIES)"
            
            # 更新DNS记录
            if update_dns_record "$current_ip"; then
                # 更新Coturn配置
                if update_coturn_config "$current_ip"; then
                    # 重启Coturn服务
                    if restart_coturn; then
                        # 验证更新
                        if verify_ip_update "$current_ip"; then
                            # 保存当前IP
                            save_current_ip "$current_ip"
                            log_info "IP更新完成: $current_ip"
                            update_success=true
                            break
                        else
                            log_warn "IP更新验证失败，但继续执行"
                            save_current_ip "$current_ip"
                            update_success=true
                            break
                        fi
                    fi
                fi
            fi
            
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $MAX_RETRIES ]]; then
                log_warn "更新失败，${RETRY_DELAY}秒后重试"
                sleep $RETRY_DELAY
            fi
        done
        
        if [[ "$update_success" == "false" ]]; then
            log_error "IP更新失败，已达到最大重试次数"
            exit 1
        fi
    else
        log_info "IP未发生变化，无需更新"
    fi
    
    log_info "IP监控检查完成"
}

# 信号处理
trap cleanup_lock EXIT
trap 'log_error "脚本被中断"; exit 1' INT TERM

# 解析参数并执行
parse_arguments "$@"

# 初始化
init_logging "$LOG_FILE"
create_lock

# 执行主逻辑
main
