#!/bin/bash

# ================================================================
# Matrix Homeserver Cloudflare API工具函数库
# ================================================================
# 功能: 提供Cloudflare DNS API操作功能
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

# 加载依赖
if [[ -f "$(dirname "${BASH_SOURCE[0]}")/common.sh" ]]; then
    source "$(dirname "${BASH_SOURCE[0]}")/common.sh"
fi

if [[ -f "$(dirname "${BASH_SOURCE[0]}")/logging.sh" ]]; then
    source "$(dirname "${BASH_SOURCE[0]}")/logging.sh"
fi

# Cloudflare API配置
CF_API_BASE="https://api.cloudflare.com/client/v4"
CF_TIMEOUT=${CF_TIMEOUT:-30}
CF_RETRIES=${CF_RETRIES:-3}

# 检查Cloudflare配置
check_cloudflare_config() {
    if [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        log_error "Cloudflare API Token未设置"
        return 1
    fi
    
    if [[ -z "${CLOUDFLARE_ZONE_ID:-}" ]]; then
        log_error "Cloudflare Zone ID未设置"
        return 1
    fi
    
    return 0
}

# 执行Cloudflare API请求
cloudflare_api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    
    if ! check_cloudflare_config; then
        return 1
    fi
    
    local url="${CF_API_BASE}${endpoint}"
    local curl_args=(
        -X "$method"
        -H "Authorization: Bearer ${CLOUDFLARE_API_TOKEN}"
        -H "Content-Type: application/json"
        --connect-timeout "$CF_TIMEOUT"
        --max-time "$((CF_TIMEOUT * 2))"
        --silent
        --show-error
    )
    
    if [[ -n "$data" ]]; then
        curl_args+=(-d "$data")
    fi
    
    log_debug "Cloudflare API请求: $method $url"
    
    local response
    if response=$(curl "${curl_args[@]}" "$url" 2>&1); then
        log_debug "Cloudflare API响应: $response"
        echo "$response"
        return 0
    else
        log_error "Cloudflare API请求失败: $response"
        return 1
    fi
}

# 检查API响应是否成功
check_api_response() {
    local response="$1"
    
    if [[ -z "$response" ]]; then
        log_error "API响应为空"
        return 1
    fi
    
    # 检查是否包含success字段
    if echo "$response" | grep -q '"success":true'; then
        return 0
    elif echo "$response" | grep -q '"success":false'; then
        local errors
        errors=$(echo "$response" | grep -o '"errors":\[.*\]' | head -1)
        log_error "API请求失败: $errors"
        return 1
    else
        log_error "无法解析API响应: $response"
        return 1
    fi
}

# 获取DNS记录列表
cloudflare_list_records() {
    local record_type="${1:-A}"
    local record_name="$2"
    
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}/dns_records"
    local params="type=${record_type}"
    
    if [[ -n "$record_name" ]]; then
        params="${params}&name=${record_name}"
    fi
    
    endpoint="${endpoint}?${params}"
    
    local response
    if response=$(cloudflare_api_request "GET" "$endpoint"); then
        if check_api_response "$response"; then
            echo "$response"
            return 0
        fi
    fi
    
    return 1
}

# 获取特定DNS记录
cloudflare_get_record() {
    local record_name="$1"
    local record_type="${2:-A}"
    
    log_debug "获取DNS记录: $record_name ($record_type)"
    
    local response
    if response=$(cloudflare_list_records "$record_type" "$record_name"); then
        # 提取第一个匹配的记录
        local record
        record=$(echo "$response" | grep -o '"result":\[.*\]' | sed 's/"result":\[//' | sed 's/\]$//' | head -1)
        
        if [[ -n "$record" ]] && [[ "$record" != "null" ]]; then
            echo "$record"
            return 0
        else
            log_debug "DNS记录不存在: $record_name"
            return 1
        fi
    fi
    
    return 1
}

# 获取DNS记录ID
cloudflare_get_record_id() {
    local record_name="$1"
    local record_type="${2:-A}"
    
    local record
    if record=$(cloudflare_get_record "$record_name" "$record_type"); then
        local record_id
        record_id=$(echo "$record" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$record_id" ]]; then
            echo "$record_id"
            return 0
        fi
    fi
    
    return 1
}

# 创建DNS记录
cloudflare_create_record() {
    local record_name="$1"
    local record_value="$2"
    local record_type="${3:-A}"
    local ttl="${4:-${DNS_TTL:-300}}"
    
    log_info "创建DNS记录: $record_name -> $record_value ($record_type)"
    
    local data
    data=$(cat << EOF
{
    "type": "$record_type",
    "name": "$record_name",
    "content": "$record_value",
    "ttl": $ttl
}
EOF
)
    
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}/dns_records"
    local response
    
    if response=$(cloudflare_api_request "POST" "$endpoint" "$data"); then
        if check_api_response "$response"; then
            log_info "DNS记录创建成功: $record_name"
            return 0
        fi
    fi
    
    log_error "DNS记录创建失败: $record_name"
    return 1
}

# 更新DNS记录
cloudflare_update_record_by_id() {
    local record_id="$1"
    local record_name="$2"
    local record_value="$3"
    local record_type="${4:-A}"
    local ttl="${5:-${DNS_TTL:-300}}"
    
    log_info "更新DNS记录: $record_name -> $record_value ($record_type)"
    
    local data
    data=$(cat << EOF
{
    "type": "$record_type",
    "name": "$record_name",
    "content": "$record_value",
    "ttl": $ttl
}
EOF
)
    
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}/dns_records/${record_id}"
    local response
    
    if response=$(cloudflare_api_request "PUT" "$endpoint" "$data"); then
        if check_api_response "$response"; then
            log_info "DNS记录更新成功: $record_name"
            return 0
        fi
    fi
    
    log_error "DNS记录更新失败: $record_name"
    return 1
}

# 删除DNS记录
cloudflare_delete_record() {
    local record_id="$1"
    
    log_info "删除DNS记录: $record_id"
    
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}/dns_records/${record_id}"
    local response
    
    if response=$(cloudflare_api_request "DELETE" "$endpoint"); then
        if check_api_response "$response"; then
            log_info "DNS记录删除成功: $record_id"
            return 0
        fi
    fi
    
    log_error "DNS记录删除失败: $record_id"
    return 1
}

# 更新或创建DNS记录
cloudflare_update_record() {
    local record_name="$1"
    local record_value="$2"
    local record_type="${3:-A}"
    local ttl="${4:-${DNS_TTL:-300}}"
    
    # 验证输入
    if [[ -z "$record_name" ]] || [[ -z "$record_value" ]]; then
        log_error "DNS记录名称和值不能为空"
        return 1
    fi
    
    if [[ "$record_type" == "A" ]] && ! validate_ip "$record_value"; then
        log_error "无效的IP地址: $record_value"
        return 1
    fi
    
    # 检查记录是否存在
    local record_id
    if record_id=$(cloudflare_get_record_id "$record_name" "$record_type"); then
        # 记录存在，更新它
        log_debug "DNS记录已存在，执行更新: $record_name (ID: $record_id)"
        cloudflare_update_record_by_id "$record_id" "$record_name" "$record_value" "$record_type" "$ttl"
    else
        # 记录不存在，创建它
        log_debug "DNS记录不存在，执行创建: $record_name"
        cloudflare_create_record "$record_name" "$record_value" "$record_type" "$ttl"
    fi
}

# 验证DNS记录
cloudflare_verify_record() {
    local record_name="$1"
    local expected_value="$2"
    local record_type="${3:-A}"
    local max_attempts="${4:-10}"
    local delay="${5:-5}"
    
    log_info "验证DNS记录: $record_name -> $expected_value"
    
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        local record
        if record=$(cloudflare_get_record "$record_name" "$record_type"); then
            local current_value
            current_value=$(echo "$record" | grep -o '"content":"[^"]*"' | cut -d'"' -f4)
            
            if [[ "$current_value" == "$expected_value" ]]; then
                log_info "DNS记录验证成功: $record_name -> $current_value"
                return 0
            else
                log_debug "DNS记录值不匹配: 期望 $expected_value，实际 $current_value (尝试 $attempt/$max_attempts)"
            fi
        else
            log_debug "无法获取DNS记录: $record_name (尝试 $attempt/$max_attempts)"
        fi
        
        if [[ $attempt -lt $max_attempts ]]; then
            sleep "$delay"
        fi
        
        attempt=$((attempt + 1))
    done
    
    log_warn "DNS记录验证失败: $record_name"
    return 1
}

# 获取区域信息
cloudflare_get_zone_info() {
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}"
    
    local response
    if response=$(cloudflare_api_request "GET" "$endpoint"); then
        if check_api_response "$response"; then
            echo "$response"
            return 0
        fi
    fi
    
    return 1
}

# 清除缓存
cloudflare_purge_cache() {
    local files=("$@")
    
    log_info "清除Cloudflare缓存"
    
    local data
    if [[ ${#files[@]} -gt 0 ]]; then
        # 清除特定文件
        local files_json
        files_json=$(printf '"%s",' "${files[@]}")
        files_json="[${files_json%,}]"
        
        data="{\"files\":$files_json}"
    else
        # 清除所有缓存
        data='{"purge_everything":true}'
    fi
    
    local endpoint="/zones/${CLOUDFLARE_ZONE_ID}/purge_cache"
    local response
    
    if response=$(cloudflare_api_request "POST" "$endpoint" "$data"); then
        if check_api_response "$response"; then
            log_info "缓存清除成功"
            return 0
        fi
    fi
    
    log_error "缓存清除失败"
    return 1
}

# 测试Cloudflare连接
cloudflare_test_connection() {
    log_info "测试Cloudflare API连接"
    
    if ! check_cloudflare_config; then
        return 1
    fi
    
    local response
    if response=$(cloudflare_get_zone_info); then
        local zone_name
        zone_name=$(echo "$response" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$zone_name" ]]; then
            log_info "Cloudflare连接测试成功，区域: $zone_name"
            return 0
        fi
    fi
    
    log_error "Cloudflare连接测试失败"
    return 1
}

# 批量更新DNS记录
cloudflare_batch_update() {
    local -A records
    
    # 解析参数 (格式: name:value:type:ttl)
    while [[ $# -gt 0 ]]; do
        local record_spec="$1"
        shift
        
        IFS=':' read -ra parts <<< "$record_spec"
        local name="${parts[0]}"
        local value="${parts[1]}"
        local type="${parts[2]:-A}"
        local ttl="${parts[3]:-${DNS_TTL:-300}}"
        
        if [[ -n "$name" ]] && [[ -n "$value" ]]; then
            records["$name"]="$value:$type:$ttl"
        fi
    done
    
    if [[ ${#records[@]} -eq 0 ]]; then
        log_error "没有有效的DNS记录需要更新"
        return 1
    fi
    
    log_info "批量更新 ${#records[@]} 个DNS记录"
    
    local success_count=0
    local total_count=${#records[@]}
    
    for name in "${!records[@]}"; do
        IFS=':' read -ra parts <<< "${records[$name]}"
        local value="${parts[0]}"
        local type="${parts[1]}"
        local ttl="${parts[2]}"
        
        if cloudflare_update_record "$name" "$value" "$type" "$ttl"; then
            success_count=$((success_count + 1))
        fi
    done
    
    log_info "批量更新完成: $success_count/$total_count 成功"
    
    if [[ $success_count -eq $total_count ]]; then
        return 0
    else
        return 1
    fi
}
