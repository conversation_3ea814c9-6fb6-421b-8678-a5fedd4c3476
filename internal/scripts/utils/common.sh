#!/bin/bash

# ================================================================
# Matrix Homeserver 通用工具函数库
# ================================================================
# 功能: 提供通用的工具函数和常量定义
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# 日志级别
readonly LOG_DEBUG=0
readonly LOG_INFO=1
readonly LOG_WARN=2
readonly LOG_ERROR=3

# 默认配置
DEFAULT_TIMEOUT=30
DEFAULT_RETRIES=3
DEFAULT_RETRY_DELAY=5

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查文件是否存在且可读
file_readable() {
    [[ -f "$1" && -r "$1" ]]
}

# 检查目录是否存在且可写
dir_writable() {
    [[ -d "$1" && -w "$1" ]]
}

# 创建目录（如果不存在）
ensure_dir() {
    local dir="$1"
    local mode="${2:-755}"
    
    if [[ ! -d "$dir" ]]; then
        if mkdir -p "$dir"; then
            chmod "$mode" "$dir"
            return 0
        else
            return 1
        fi
    fi
    return 0
}

# 安全地删除文件
safe_remove() {
    local file="$1"
    
    if [[ -f "$file" ]]; then
        rm -f "$file"
    fi
}

# 安全地移动文件
safe_move() {
    local src="$1"
    local dst="$2"
    
    if [[ -f "$src" ]]; then
        mv "$src" "$dst"
        return $?
    else
        return 1
    fi
}

# 生成随机字符串
generate_random_string() {
    local length="${1:-32}"
    openssl rand -base64 "$length" | tr -d "=+/" | cut -c1-"$length"
}

# 验证IP地址格式
validate_ip() {
    local ip="$1"
    local regex='^([0-9]{1,3}\.){3}[0-9]{1,3}$'
    
    if [[ $ip =~ $regex ]]; then
        # 检查每个八位组是否在0-255范围内
        IFS='.' read -ra ADDR <<< "$ip"
        for i in "${ADDR[@]}"; do
            if [[ $i -gt 255 ]]; then
                return 1
            fi
        done
        return 0
    else
        return 1
    fi
}

# 验证域名格式
validate_domain() {
    local domain="$1"
    local regex='^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    
    [[ $domain =~ $regex ]]
}

# 验证端口号
validate_port() {
    local port="$1"
    
    if [[ "$port" =~ ^[0-9]+$ ]] && [[ "$port" -ge 1 ]] && [[ "$port" -le 65535 ]]; then
        return 0
    else
        return 1
    fi
}

# 检查端口是否被占用
port_in_use() {
    local port="$1"
    local protocol="${2:-tcp}"
    
    if command_exists netstat; then
        netstat -ln | grep -q ":$port "
    elif command_exists ss; then
        ss -ln | grep -q ":$port "
    else
        # 使用lsof作为备选
        if command_exists lsof; then
            lsof -i ":$port" >/dev/null 2>&1
        else
            return 1
        fi
    fi
}

# 等待端口可用
wait_for_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-30}"
    local interval="${4:-1}"
    
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        if timeout 1 bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
            return 0
        fi
        sleep "$interval"
        elapsed=$((elapsed + interval))
    done
    
    return 1
}

# 检查服务是否运行
service_running() {
    local service="$1"
    
    if command_exists systemctl; then
        systemctl is-active --quiet "$service"
    elif command_exists service; then
        service "$service" status >/dev/null 2>&1
    else
        return 1
    fi
}

# 检查Docker容器状态
container_running() {
    local container="$1"
    
    if command_exists docker; then
        docker ps --format "table {{.Names}}" | grep -q "^$container$"
    else
        return 1
    fi
}

# 获取容器IP地址
get_container_ip() {
    local container="$1"
    
    if command_exists docker; then
        docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' "$container" 2>/dev/null
    fi
}

# 执行带重试的命令
retry_command() {
    local max_attempts="${1:-$DEFAULT_RETRIES}"
    local delay="${2:-$DEFAULT_RETRY_DELAY}"
    shift 2
    
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if "$@"; then
            return 0
        fi
        
        if [[ $attempt -lt $max_attempts ]]; then
            sleep "$delay"
        fi
        
        attempt=$((attempt + 1))
    done
    
    return 1
}

# 执行带超时的命令
timeout_command() {
    local timeout="${1:-$DEFAULT_TIMEOUT}"
    shift
    
    if command_exists timeout; then
        timeout "$timeout" "$@"
    else
        # 简单的超时实现
        "$@" &
        local pid=$!
        
        (
            sleep "$timeout"
            kill -TERM "$pid" 2>/dev/null
        ) &
        local killer_pid=$!
        
        wait "$pid"
        local exit_code=$?
        
        kill -TERM "$killer_pid" 2>/dev/null
        wait "$killer_pid" 2>/dev/null
        
        return $exit_code
    fi
}

# 检查磁盘空间
check_disk_space() {
    local path="${1:-.}"
    local min_space_gb="${2:-1}"
    
    local available_kb
    available_kb=$(df "$path" | awk 'NR==2 {print $4}')
    local available_gb=$((available_kb / 1024 / 1024))
    
    [[ $available_gb -ge $min_space_gb ]]
}

# 检查内存使用
check_memory_usage() {
    local max_usage_percent="${1:-90}"
    
    local mem_info
    mem_info=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    [[ $mem_info -le $max_usage_percent ]]
}

# 获取系统负载
get_system_load() {
    uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ','
}

# 检查网络连接
check_network_connectivity() {
    local host="${1:-*******}"
    local timeout="${2:-5}"
    
    if command_exists ping; then
        ping -c 1 -W "$timeout" "$host" >/dev/null 2>&1
    elif command_exists nc; then
        nc -z -w "$timeout" "$host" 53 >/dev/null 2>&1
    else
        return 1
    fi
}

# 获取当前时间戳
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 获取ISO格式时间戳
get_iso_timestamp() {
    date -u '+%Y-%m-%dT%H:%M:%SZ'
}

# 计算文件哈希
calculate_file_hash() {
    local file="$1"
    local algorithm="${2:-sha256}"
    
    if command_exists "${algorithm}sum"; then
        "${algorithm}sum" "$file" | awk '{print $1}'
    elif command_exists openssl; then
        openssl dgst -"$algorithm" "$file" | awk '{print $2}'
    else
        return 1
    fi
}

# 比较版本号
version_compare() {
    local version1="$1"
    local version2="$2"
    
    if [[ "$version1" == "$version2" ]]; then
        echo "0"
        return
    fi
    
    local IFS=.
    local i ver1=($version1) ver2=($version2)
    
    # 填充较短的版本号
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++)); do
        ver1[i]=0
    done
    for ((i=${#ver2[@]}; i<${#ver1[@]}; i++)); do
        ver2[i]=0
    done
    
    # 比较每个部分
    for ((i=0; i<${#ver1[@]}; i++)); do
        if [[ -z ${ver2[i]} ]]; then
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]})); then
            echo "1"
            return
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]})); then
            echo "-1"
            return
        fi
    done
    
    echo "0"
}

# 清理临时文件
cleanup_temp_files() {
    local temp_dir="${1:-/tmp}"
    local pattern="${2:-matrix_*}"
    local max_age="${3:-1}"  # 天数
    
    find "$temp_dir" -name "$pattern" -type f -mtime +"$max_age" -delete 2>/dev/null || true
}

# 发送通知
send_notification() {
    local title="$1"
    local message="$2"
    local level="${3:-info}"
    
    # 这里可以集成各种通知方式
    # 例如: email, webhook, slack等
    
    # 简单的日志记录
    case "$level" in
        error)
            echo "[ERROR] $title: $message" >&2
            ;;
        warn)
            echo "[WARN] $title: $message" >&2
            ;;
        *)
            echo "[INFO] $title: $message"
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    local deps=("$@")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command_exists "$dep"; then
            missing+=("$dep")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        echo "缺少依赖: ${missing[*]}" >&2
        return 1
    fi
    
    return 0
}
