#!/bin/bash

# ================================================================
# Matrix Homeserver 日志工具函数库
# ================================================================
# 功能: 提供统一的日志记录功能
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

# 加载通用函数
if [[ -f "$(dirname "${BASH_SOURCE[0]}")/common.sh" ]]; then
    source "$(dirname "${BASH_SOURCE[0]}")/common.sh"
fi

# 日志配置
LOG_FILE=""
LOG_LEVEL=${LOG_LEVEL:-$LOG_INFO}
LOG_TO_CONSOLE=${LOG_TO_CONSOLE:-true}
LOG_TO_FILE=${LOG_TO_FILE:-true}
LOG_MAX_SIZE=${LOG_MAX_SIZE:-10485760}  # 10MB
LOG_BACKUP_COUNT=${LOG_BACKUP_COUNT:-5}

# 日志级别名称
declare -A LOG_LEVEL_NAMES=(
    [$LOG_DEBUG]="DEBUG"
    [$LOG_INFO]="INFO"
    [$LOG_WARN]="WARN"
    [$LOG_ERROR]="ERROR"
)

# 日志级别颜色
declare -A LOG_LEVEL_COLORS=(
    [$LOG_DEBUG]="$CYAN"
    [$LOG_INFO]="$GREEN"
    [$LOG_WARN]="$YELLOW"
    [$LOG_ERROR]="$RED"
)

# 初始化日志系统
init_logging() {
    local log_file="$1"
    local log_level="${2:-$LOG_INFO}"
    
    LOG_FILE="$log_file"
    LOG_LEVEL="$log_level"
    
    # 创建日志目录
    if [[ -n "$LOG_FILE" ]]; then
        local log_dir
        log_dir=$(dirname "$LOG_FILE")
        ensure_dir "$log_dir" 755
        
        # 检查日志文件大小并轮转
        rotate_log_if_needed
    fi
}

# 日志轮转
rotate_log_if_needed() {
    if [[ -z "$LOG_FILE" ]] || [[ ! -f "$LOG_FILE" ]]; then
        return 0
    fi
    
    local file_size
    file_size=$(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0)
    
    if [[ $file_size -gt $LOG_MAX_SIZE ]]; then
        rotate_logs
    fi
}

# 执行日志轮转
rotate_logs() {
    if [[ -z "$LOG_FILE" ]] || [[ ! -f "$LOG_FILE" ]]; then
        return 0
    fi
    
    local log_dir log_name log_ext
    log_dir=$(dirname "$LOG_FILE")
    log_name=$(basename "$LOG_FILE" .log)
    log_ext=".log"
    
    # 轮转现有日志文件
    for ((i=$LOG_BACKUP_COUNT; i>=1; i--)); do
        local old_file="${log_dir}/${log_name}.${i}${log_ext}"
        local new_file="${log_dir}/${log_name}.$((i+1))${log_ext}"
        
        if [[ -f "$old_file" ]]; then
            if [[ $i -eq $LOG_BACKUP_COUNT ]]; then
                rm -f "$old_file"
            else
                mv "$old_file" "$new_file"
            fi
        fi
    done
    
    # 移动当前日志文件
    mv "$LOG_FILE" "${log_dir}/${log_name}.1${log_ext}"
    
    # 创建新的日志文件
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"
}

# 格式化日志消息
format_log_message() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(get_timestamp)
    
    local level_name="${LOG_LEVEL_NAMES[$level]:-UNKNOWN}"
    local script_name
    script_name=$(basename "${BASH_SOURCE[3]}" .sh 2>/dev/null || echo "unknown")
    
    echo "[$timestamp] [$level_name] [$script_name] $message"
}

# 写入日志
write_log() {
    local level="$1"
    local message="$2"
    
    # 检查日志级别
    if [[ $level -lt $LOG_LEVEL ]]; then
        return 0
    fi
    
    local formatted_message
    formatted_message=$(format_log_message "$level" "$message")
    
    # 输出到控制台
    if [[ "$LOG_TO_CONSOLE" == "true" ]]; then
        local color="${LOG_LEVEL_COLORS[$level]:-$NC}"
        echo -e "${color}${formatted_message}${NC}" >&2
    fi
    
    # 输出到文件
    if [[ "$LOG_TO_FILE" == "true" ]] && [[ -n "$LOG_FILE" ]]; then
        echo "$formatted_message" >> "$LOG_FILE"
        
        # 检查是否需要轮转
        rotate_log_if_needed
    fi
}

# 日志函数
log_debug() {
    write_log $LOG_DEBUG "$*"
}

log_info() {
    write_log $LOG_INFO "$*"
}

log_warn() {
    write_log $LOG_WARN "$*"
}

log_error() {
    write_log $LOG_ERROR "$*"
}

# 带退出的错误日志
log_fatal() {
    write_log $LOG_ERROR "$*"
    exit 1
}

# 条件日志
log_if() {
    local condition="$1"
    local level="$2"
    shift 2
    
    if [[ "$condition" == "true" ]] || [[ "$condition" == "1" ]]; then
        write_log "$level" "$*"
    fi
}

# 性能日志
log_performance() {
    local operation="$1"
    local start_time="$2"
    local end_time="${3:-$(date +%s.%N)}"
    
    local duration
    duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "unknown")
    
    log_info "性能统计: $operation 耗时 ${duration}s"
}

# 开始性能计时
start_timer() {
    date +%s.%N
}

# 结束性能计时并记录
end_timer() {
    local operation="$1"
    local start_time="$2"
    local end_time
    end_time=$(date +%s.%N)
    
    log_performance "$operation" "$start_time" "$end_time"
}

# 记录函数执行
log_function_call() {
    local func_name="$1"
    shift
    
    log_debug "调用函数: $func_name($*)"
}

# 记录变量值
log_variable() {
    local var_name="$1"
    local var_value="$2"
    local sensitive="${3:-false}"
    
    if [[ "$sensitive" == "true" ]]; then
        log_debug "变量: $var_name=[REDACTED]"
    else
        log_debug "变量: $var_name=$var_value"
    fi
}

# 记录命令执行
log_command() {
    local command="$1"
    local hide_output="${2:-false}"
    
    log_debug "执行命令: $command"
    
    if [[ "$hide_output" == "true" ]]; then
        eval "$command" 2>&1 | while IFS= read -r line; do
            log_debug "命令输出: $line"
        done
        return ${PIPESTATUS[0]}
    else
        eval "$command"
    fi
}

# 记录HTTP请求
log_http_request() {
    local method="$1"
    local url="$2"
    local status_code="$3"
    local response_time="$4"
    
    log_info "HTTP请求: $method $url -> $status_code (${response_time}ms)"
}

# 记录系统信息
log_system_info() {
    log_info "系统信息收集开始"
    
    # 操作系统信息
    if [[ -f /etc/os-release ]]; then
        local os_info
        os_info=$(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)
        log_info "操作系统: $os_info"
    fi
    
    # 内核版本
    log_info "内核版本: $(uname -r)"
    
    # 系统负载
    log_info "系统负载: $(get_system_load)"
    
    # 内存使用
    local mem_info
    mem_info=$(free -h | awk 'NR==2{printf "使用: %s/%s (%.0f%%)", $3,$2,$3*100/$2}')
    log_info "内存使用: $mem_info"
    
    # 磁盘使用
    log_info "磁盘使用:"
    df -h | grep -E '^/dev/' | while read -r line; do
        log_info "  $line"
    done
    
    log_info "系统信息收集完成"
}

# 记录错误堆栈
log_error_stack() {
    local error_message="$1"
    
    log_error "错误: $error_message"
    log_error "调用堆栈:"
    
    local frame=1
    while caller $frame; do
        frame=$((frame + 1))
    done | while read -r line_no func_name file_name; do
        log_error "  在 $file_name:$line_no 的 $func_name()"
    done
}

# 创建日志上下文
create_log_context() {
    local context_name="$1"
    local context_id="${2:-$(generate_random_string 8)}"
    
    export LOG_CONTEXT="[$context_name:$context_id]"
}

# 清除日志上下文
clear_log_context() {
    unset LOG_CONTEXT
}

# 带上下文的日志函数
log_with_context() {
    local level="$1"
    shift
    
    local message="$*"
    if [[ -n "${LOG_CONTEXT:-}" ]]; then
        message="$LOG_CONTEXT $message"
    fi
    
    write_log "$level" "$message"
}

# 结构化日志
log_structured() {
    local level="$1"
    local event="$2"
    shift 2
    
    local fields=()
    while [[ $# -gt 0 ]]; do
        fields+=("$1=$2")
        shift 2
    done
    
    local message="event=$event"
    if [[ ${#fields[@]} -gt 0 ]]; then
        message="$message ${fields[*]}"
    fi
    
    write_log "$level" "$message"
}

# 日志统计
log_stats() {
    if [[ -z "$LOG_FILE" ]] || [[ ! -f "$LOG_FILE" ]]; then
        log_warn "日志文件不存在，无法生成统计"
        return 1
    fi
    
    log_info "日志统计:"
    
    # 按级别统计
    for level in DEBUG INFO WARN ERROR; do
        local count
        count=$(grep -c "\[$level\]" "$LOG_FILE" 2>/dev/null || echo 0)
        log_info "  $level: $count 条"
    done
    
    # 文件大小
    local file_size
    file_size=$(du -h "$LOG_FILE" | cut -f1)
    log_info "  文件大小: $file_size"
    
    # 最新日志时间
    local last_log
    last_log=$(tail -1 "$LOG_FILE" 2>/dev/null | grep -o '\[.*\]' | head -1 | tr -d '[]' || echo "无")
    log_info "  最新日志: $last_log"
}

# 清理旧日志
cleanup_old_logs() {
    local log_dir="${1:-$(dirname "$LOG_FILE")}"
    local max_age_days="${2:-30}"
    
    if [[ -z "$log_dir" ]] || [[ ! -d "$log_dir" ]]; then
        log_warn "日志目录不存在: $log_dir"
        return 1
    fi
    
    log_info "清理 $max_age_days 天前的日志文件"
    
    local cleaned_count=0
    while IFS= read -r -d '' file; do
        rm -f "$file"
        cleaned_count=$((cleaned_count + 1))
        log_debug "删除旧日志: $file"
    done < <(find "$log_dir" -name "*.log.*" -type f -mtime +"$max_age_days" -print0 2>/dev/null)
    
    log_info "清理完成，删除了 $cleaned_count 个旧日志文件"
}
