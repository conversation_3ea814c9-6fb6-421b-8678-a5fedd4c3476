#!/usr/bin/env python3
"""
RouterOS API客户端 - 内部部署包版本
基于官方routeros-api库的简化实现
仅包含IP获取功能，用于Matrix Homeserver部署
"""

import sys
import logging
import ipaddress
from typing import Optional

# 配置日志为静默模式
logging.getLogger().setLevel(logging.CRITICAL)

try:
    import routeros_api
except ImportError:
    # 静默失败，由调用脚本处理
    sys.exit(2)


def get_wan_ip(host: str, username: str = 'admin', password: str = '', 
               port: int = 8728, timeout: int = 10, 
               interface_name: Optional[str] = None) -> Optional[str]:
    """
    获取RouterOS设备的WAN接口IP地址
    
    Args:
        host: RouterOS设备IP地址
        username: 用户名
        password: 密码
        port: API端口
        timeout: 超时时间
        interface_name: 指定的接口名称
        
    Returns:
        Optional[str]: WAN接口的公网IP地址，失败返回None
    """
    connection = None
    
    try:
        # 连接RouterOS
        connection = routeros_api.RouterOsApiPool(
            host=host,
            username=username,
            password=password,
            port=port,
            plaintext_login=True,  # RouterOS 6.43+必需
            use_ssl=False,
            ssl_verify=False,
            ssl_verify_hostname=False
        )
        
        api = connection.get_api()
        
        # 获取所有接口
        interfaces = api.get_resource('/interface').get()
        
        # 检测WAN接口
        wan_interface = None
        
        # 1. 用户指定的接口
        if interface_name:
            for interface in interfaces:
                if interface.get('name') == interface_name:
                    wan_interface = interface_name
                    break
        
        # 2. 自动检测
        if not wan_interface:
            # 查找默认WAN接口名称
            for interface in interfaces:
                name = interface.get('name', '')
                if name == 'WAN':
                    wan_interface = name
                    break

            # 查找包含'wan'的接口
            if not wan_interface:
                for interface in interfaces:
                    name = interface.get('name', '').lower()
                    if 'wan' in name:
                        wan_interface = interface.get('name')
                        break
            
            # 查找第一个以太网接口
            if not wan_interface:
                for interface in interfaces:
                    name = interface.get('name', '')
                    if name.startswith('ether') and interface.get('type') == 'ether':
                        wan_interface = name
                        break
            
            # 查找PPPoE接口
            if not wan_interface:
                for interface in interfaces:
                    name = interface.get('name', '')
                    if 'pppoe' in name.lower():
                        wan_interface = name
                        break
        
        if not wan_interface:
            return None
        
        # 获取接口IP地址
        addresses = api.get_resource('/ip/address').get()
        
        for addr in addresses:
            if addr.get('interface') == wan_interface:
                address = addr.get('address', '')
                if '/' in address:
                    ip = address.split('/')[0]
                    
                    # 验证是否为公网IP
                    try:
                        ip_obj = ipaddress.IPv4Address(ip)
                        if not (ip_obj.is_private or ip_obj.is_loopback or 
                               ip_obj.is_link_local or ip_obj.is_multicast or 
                               ip_obj.is_reserved):
                            return ip
                    except (ipaddress.AddressValueError, ValueError):
                        continue
        
        return None
        
    except Exception:
        return None
    finally:
        if connection:
            try:
                connection.disconnect()
            except:
                pass


def main():
    """命令行接口"""
    if len(sys.argv) < 4:
        print("用法: routeros_client.py <host> <username> <password> [port] [interface]", file=sys.stderr)
        sys.exit(1)
    
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    port = int(sys.argv[4]) if len(sys.argv) > 4 else 8728
    interface_name = sys.argv[5] if len(sys.argv) > 5 else None
    
    wan_ip = get_wan_ip(host, username, password, port, 10, interface_name)
    
    if wan_ip:
        print(wan_ip)
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
