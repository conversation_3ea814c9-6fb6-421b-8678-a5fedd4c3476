#!/bin/bash

# ================================================================
# Matrix Homeserver 健康检查脚本
# ================================================================
# 功能: 全面检查Matrix Homeserver系统的健康状态
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载配置
if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
    source "$PROJECT_DIR/config/deployment.env"
else
    echo "错误: 配置文件 $PROJECT_DIR/config/deployment.env 不存在"
    exit 1
fi

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"
source "$SCRIPT_DIR/utils/routeros_api.sh"

# 默认配置
LOG_FILE="${LOG_DIR}/health_check.log"
DETAILED_CHECK=false
FIX_ISSUES=false

# 健康检查结果
declare -A HEALTH_STATUS
OVERALL_HEALTH="HEALTHY"

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --detailed)
                DETAILED_CHECK=true
                shift
                ;;
            --fix)
                FIX_ISSUES=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 健康检查脚本

用法: $0 [选项]

选项:
    --detailed      执行详细的健康检查
    --fix           尝试自动修复发现的问题
    --help          显示此帮助信息

示例:
    $0                    # 基础健康检查
    $0 --detailed         # 详细健康检查
    $0 --detailed --fix   # 详细检查并尝试修复问题

EOF
}

# 初始化日志
init_logging "$LOG_FILE"

# 记录检查结果
record_check_result() {
    local component="$1"
    local status="$2"
    local message="$3"
    
    HEALTH_STATUS["$component"]="$status"
    
    case "$status" in
        "HEALTHY")
            log_info "✓ $component: $message"
            ;;
        "WARNING")
            log_warn "⚠ $component: $message"
            if [[ "$OVERALL_HEALTH" == "HEALTHY" ]]; then
                OVERALL_HEALTH="WARNING"
            fi
            ;;
        "CRITICAL")
            log_error "✗ $component: $message"
            OVERALL_HEALTH="CRITICAL"
            ;;
    esac
}

# 检查Docker服务
check_docker_services() {
    log_info "检查Docker服务状态"
    
    local services=("matrix_postgres" "matrix_redis" "matrix_synapse" "matrix_nginx" "matrix_coturn")
    local healthy_count=0
    
    for service in "${services[@]}"; do
        if container_running "$service"; then
            record_check_result "$service" "HEALTHY" "容器运行正常"
            healthy_count=$((healthy_count + 1))
        else
            record_check_result "$service" "CRITICAL" "容器未运行"
            
            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试重启服务: $service"
                docker compose -f "$PROJECT_DIR/docker-compose.yml" restart "${service#matrix_}"
            fi
        fi
    done
    
    if [[ $healthy_count -eq ${#services[@]} ]]; then
        record_check_result "docker_services" "HEALTHY" "所有Docker服务运行正常 ($healthy_count/${#services[@]})"
    else
        record_check_result "docker_services" "CRITICAL" "部分Docker服务异常 ($healthy_count/${#services[@]})"
    fi
}

# 检查证书状态
check_certificates() {
    log_info "检查SSL证书状态"
    
    # 检查证书有效期
    local cert_status
    cert_status=$("${SCRIPT_DIR}/certificate_manager.sh" --check-expiry 2>/dev/null | grep "证书状态:" | cut -d: -f2 | xargs || echo "检查失败")
    
    case "$cert_status" in
        *"天到期")
            local days=$(echo "$cert_status" | grep -o '[0-9]\+')
            if [[ $days -gt 30 ]]; then
                record_check_result "certificate_expiry" "HEALTHY" "证书有效期充足 ($cert_status)"
            elif [[ $days -gt 7 ]]; then
                record_check_result "certificate_expiry" "WARNING" "证书即将到期 ($cert_status)"
            else
                record_check_result "certificate_expiry" "CRITICAL" "证书即将到期 ($cert_status)"
                
                if [[ "$FIX_ISSUES" == "true" ]]; then
                    log_info "尝试续期证书"
                    "${SCRIPT_DIR}/certificate_manager.sh" --force-renew
                fi
            fi
            ;;
        *"已过期"*)
            record_check_result "certificate_expiry" "CRITICAL" "证书已过期 ($cert_status)"
            
            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试续期过期证书"
                "${SCRIPT_DIR}/certificate_manager.sh" --force-renew
            fi
            ;;
        *"不存在"*)
            record_check_result "certificate_expiry" "CRITICAL" "证书不存在"
            
            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试申请新证书"
                "${SCRIPT_DIR}/certificate_manager.sh"
            fi
            ;;
        *)
            record_check_result "certificate_expiry" "WARNING" "无法检查证书状态"
            ;;
    esac
    
    # 检查证书符号链接
    if "${SCRIPT_DIR}/init_certificate_links.sh" status >/dev/null 2>&1; then
        record_check_result "certificate_links" "HEALTHY" "证书符号链接正常"
    else
        record_check_result "certificate_links" "CRITICAL" "证书符号链接异常"
        
        if [[ "$FIX_ISSUES" == "true" ]]; then
            log_info "尝试修复证书符号链接"
            "${SCRIPT_DIR}/init_certificate_links.sh" cleanup
            "${SCRIPT_DIR}/init_certificate_links.sh" init
        fi
    fi
}

# 检查网络连接
check_network_connectivity() {
    log_info "检查网络连接"
    
    # 检查RouterOS连接 (如果配置了)
    if [[ -n "${ROUTEROS_HOST:-}" ]] && [[ -n "${ROUTEROS_USER:-}" ]] && [[ -n "${ROUTEROS_PASSWORD:-}" ]]; then
        if test_routeros_connection >/dev/null 2>&1; then
            record_check_result "routeros_connection" "HEALTHY" "RouterOS API连接正常"

            # 尝试从RouterOS获取IP
            local routeros_ip
            if routeros_ip=$(get_routeros_wan_ip 2>/dev/null); then
                record_check_result "routeros_wan_ip" "HEALTHY" "RouterOS WAN IP: $routeros_ip"
                current_ip="$routeros_ip"
            else
                record_check_result "routeros_wan_ip" "WARNING" "无法从RouterOS获取WAN IP"
            fi
        else
            record_check_result "routeros_connection" "WARNING" "RouterOS API连接失败"
        fi
    fi

    # 检查公网IP (如果RouterOS未配置或失败)
    if [[ -z "${current_ip:-}" ]]; then
        if current_ip=$(curl -s --connect-timeout 10 https://ipv4.icanhazip.com/ 2>/dev/null); then
            record_check_result "public_ip" "HEALTHY" "公网IP获取正常: $current_ip"
        else
            record_check_result "public_ip" "CRITICAL" "无法获取公网IP"
        fi
    fi

    # 检查IP是否变化
    if [[ -n "${current_ip:-}" ]]; then
        local last_ip
        last_ip=$(cat "${DATA_DIR}/last_ip.txt" 2>/dev/null || echo "")
        if [[ -n "$last_ip" ]] && [[ "$current_ip" != "$last_ip" ]]; then
            record_check_result "ip_change" "WARNING" "检测到IP变化: $last_ip -> $current_ip"

            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试更新IP配置"
                "${SCRIPT_DIR}/ip_watchdog.sh" --force-update
            fi
        else
            record_check_result "ip_change" "HEALTHY" "IP地址稳定"
        fi
    fi
    
    # 检查DNS解析
    local matrix_domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    if nslookup "$matrix_domain" >/dev/null 2>&1; then
        record_check_result "dns_resolution" "HEALTHY" "DNS解析正常: $matrix_domain"
    else
        record_check_result "dns_resolution" "CRITICAL" "DNS解析失败: $matrix_domain"
    fi
}

# 检查Matrix服务
check_matrix_services() {
    log_info "检查Matrix服务功能"
    
    local matrix_url="https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}"
    
    # 检查客户端API
    if curl -k -s --connect-timeout 10 "${matrix_url}/_matrix/client/versions" >/dev/null 2>&1; then
        record_check_result "matrix_client_api" "HEALTHY" "Matrix客户端API正常"
    else
        record_check_result "matrix_client_api" "CRITICAL" "Matrix客户端API异常"
    fi
    
    # 检查联邦API
    if curl -k -s --connect-timeout 10 "${matrix_url}/_matrix/federation/v1/version" >/dev/null 2>&1; then
        record_check_result "matrix_federation_api" "HEALTHY" "Matrix联邦API正常"
    else
        record_check_result "matrix_federation_api" "CRITICAL" "Matrix联邦API异常"
    fi
    
    # 检查.well-known文件
    if curl -s --connect-timeout 10 "https://${DOMAIN}/.well-known/matrix/client" >/dev/null 2>&1; then
        record_check_result "wellknown_client" "HEALTHY" ".well-known客户端配置正常"
    else
        record_check_result "wellknown_client" "WARNING" ".well-known客户端配置异常"
    fi
    
    if curl -s --connect-timeout 10 "https://${DOMAIN}/.well-known/matrix/server" >/dev/null 2>&1; then
        record_check_result "wellknown_server" "HEALTHY" ".well-known服务器配置正常"
    else
        record_check_result "wellknown_server" "WARNING" ".well-known服务器配置异常"
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源"
    
    # 检查磁盘空间
    local disk_usage
    disk_usage=$(df "$PROJECT_DIR" | awk 'NR==2 {print $5}' | tr -d '%')
    
    if [[ $disk_usage -lt 80 ]]; then
        record_check_result "disk_space" "HEALTHY" "磁盘使用率正常: ${disk_usage}%"
    elif [[ $disk_usage -lt 90 ]]; then
        record_check_result "disk_space" "WARNING" "磁盘使用率较高: ${disk_usage}%"
    else
        record_check_result "disk_space" "CRITICAL" "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查内存使用
    local mem_usage
    mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [[ $mem_usage -lt 80 ]]; then
        record_check_result "memory_usage" "HEALTHY" "内存使用率正常: ${mem_usage}%"
    elif [[ $mem_usage -lt 90 ]]; then
        record_check_result "memory_usage" "WARNING" "内存使用率较高: ${mem_usage}%"
    else
        record_check_result "memory_usage" "CRITICAL" "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查系统负载
    local load_avg
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
    local cpu_cores
    cpu_cores=$(nproc)
    local load_ratio
    load_ratio=$(echo "$load_avg * 100 / $cpu_cores" | bc -l | cut -d. -f1)
    
    if [[ $load_ratio -lt 70 ]]; then
        record_check_result "system_load" "HEALTHY" "系统负载正常: $load_avg (${load_ratio}%)"
    elif [[ $load_ratio -lt 90 ]]; then
        record_check_result "system_load" "WARNING" "系统负载较高: $load_avg (${load_ratio}%)"
    else
        record_check_result "system_load" "CRITICAL" "系统负载过高: $load_avg (${load_ratio}%)"
    fi
}

# 检查数据库状态
check_database() {
    if [[ "$DETAILED_CHECK" != "true" ]]; then
        return 0
    fi
    
    log_info "检查数据库状态"
    
    # 检查PostgreSQL连接
    if docker compose -f "$PROJECT_DIR/docker-compose.yml" exec db pg_isready -U "${DB_USER}" >/dev/null 2>&1; then
        record_check_result "database_connection" "HEALTHY" "数据库连接正常"
        
        # 检查数据库大小
        local db_size
        db_size=$(docker compose -f "$PROJECT_DIR/docker-compose.yml" exec db \
            psql -U "${DB_USER}" -d "${DB_NAME}" -t -c "SELECT pg_size_pretty(pg_database_size('${DB_NAME}'));" 2>/dev/null | xargs || echo "未知")
        
        record_check_result "database_size" "HEALTHY" "数据库大小: $db_size"
        
        # 检查活跃连接数
        local active_connections
        active_connections=$(docker compose -f "$PROJECT_DIR/docker-compose.yml" exec db \
            psql -U "${DB_USER}" -d "${DB_NAME}" -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs || echo "0")
        
        if [[ $active_connections -lt 50 ]]; then
            record_check_result "database_connections" "HEALTHY" "活跃连接数正常: $active_connections"
        else
            record_check_result "database_connections" "WARNING" "活跃连接数较高: $active_connections"
        fi
    else
        record_check_result "database_connection" "CRITICAL" "数据库连接失败"
    fi
}

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告"
    
    echo ""
    echo "=========================================="
    echo "Matrix Homeserver 健康检查报告"
    echo "=========================================="
    echo "检查时间: $(date)"
    echo "总体状态: $OVERALL_HEALTH"
    echo ""
    
    # 按状态分组显示结果
    echo "详细检查结果:"
    echo ""
    
    for status in "CRITICAL" "WARNING" "HEALTHY"; do
        local found=false
        for component in "${!HEALTH_STATUS[@]}"; do
            if [[ "${HEALTH_STATUS[$component]}" == "$status" ]]; then
                if [[ "$found" == "false" ]]; then
                    echo "[$status]"
                    found=true
                fi
                echo "  - $component: ${HEALTH_STATUS[$component]}"
            fi
        done
        if [[ "$found" == "true" ]]; then
            echo ""
        fi
    done
    
    # 提供建议
    if [[ "$OVERALL_HEALTH" == "CRITICAL" ]]; then
        echo "建议操作:"
        echo "  1. 检查关键服务状态: docker compose ps"
        echo "  2. 查看服务日志: docker compose logs"
        echo "  3. 尝试自动修复: $0 --detailed --fix"
        echo ""
    elif [[ "$OVERALL_HEALTH" == "WARNING" ]]; then
        echo "建议操作:"
        echo "  1. 监控警告项目的发展趋势"
        echo "  2. 考虑在维护窗口进行优化"
        echo "  3. 定期执行详细检查: $0 --detailed"
        echo ""
    fi
    
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始Matrix Homeserver健康检查"
    
    # 基础检查
    check_docker_services
    check_certificates
    check_network_connectivity
    check_matrix_services
    check_system_resources
    
    # 详细检查
    if [[ "$DETAILED_CHECK" == "true" ]]; then
        check_database
    fi
    
    # 生成报告
    generate_health_report
    
    log_info "健康检查完成，总体状态: $OVERALL_HEALTH"
    
    # 根据健康状态设置退出码
    case "$OVERALL_HEALTH" in
        "HEALTHY") exit 0 ;;
        "WARNING") exit 1 ;;
        "CRITICAL") exit 2 ;;
    esac
}

# 解析参数并执行
parse_arguments "$@"
main
