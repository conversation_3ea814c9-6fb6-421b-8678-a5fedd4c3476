#!/bin/bash

# ================================================================
# RouterOS API 配置和测试脚本
# ================================================================
# 功能: 配置和测试RouterOS API连接
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"
source "$SCRIPT_DIR/utils/routeros_api.sh"

# 默认配置
LOG_FILE="${LOG_DIR}/routeros_setup.log"

# 显示帮助信息
show_help() {
    cat << EOF
RouterOS API 配置和测试脚本

用法: $0 <命令> [选项]

命令:
    configure                配置RouterOS API连接
    test                     测试RouterOS API连接
    get-ip                   获取WAN接口IP地址
    list-interfaces          列出所有网络接口
    list-addresses           列出所有IP地址
    enable-api               在RouterOS上启用API服务
    create-user              创建API用户

选项:
    --host <ip>              RouterOS主机地址
    --port <port>            API端口 (默认8728)
    --user <username>        用户名
    --password <password>    密码
    --help                   显示此帮助信息

示例:
    $0 configure                                    # 交互式配置
    $0 test                                         # 测试连接
    $0 get-ip                                       # 获取WAN IP
    $0 test --host *********** --user admin        # 指定参数测试

EOF
}

# 初始化日志
init_logging "$LOG_FILE"

# 交互式配置RouterOS连接
configure_routeros() {
    log_info "配置RouterOS API连接"
    
    local config_file="$PROJECT_DIR/config/deployment.env"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "请先运行初始化脚本: ./scripts/setup.sh"
        return 1
    fi
    
    echo "RouterOS API配置向导"
    echo "===================="
    echo ""
    
    # 获取RouterOS主机地址
    local routeros_host
    read -p "请输入RouterOS主机地址 (例: ***********): " routeros_host
    
    if [[ -z "$routeros_host" ]]; then
        log_error "主机地址不能为空"
        return 1
    fi
    
    if ! validate_ip "$routeros_host"; then
        log_error "无效的IP地址格式: $routeros_host"
        return 1
    fi
    
    # 获取API端口
    local routeros_port
    read -p "请输入API端口 [8728]: " routeros_port
    routeros_port=${routeros_port:-8728}
    
    if ! validate_port "$routeros_port"; then
        log_error "无效的端口号: $routeros_port"
        return 1
    fi
    
    # 获取用户名
    local routeros_user
    read -p "请输入用户名 [admin]: " routeros_user
    routeros_user=${routeros_user:-admin}
    
    # 获取密码
    local routeros_password
    echo -n "请输入密码: "
    read -s routeros_password
    echo ""
    
    if [[ -z "$routeros_password" ]]; then
        log_error "密码不能为空"
        return 1
    fi
    
    # 测试连接
    echo ""
    log_info "测试RouterOS连接..."
    
    export ROUTEROS_HOST="$routeros_host"
    export ROUTEROS_PORT="$routeros_port"
    export ROUTEROS_USER="$routeros_user"
    export ROUTEROS_PASSWORD="$routeros_password"
    
    if test_routeros_connection; then
        log_info "连接测试成功！"
        
        # 更新配置文件
        log_info "更新配置文件..."
        
        # 备份原配置文件
        cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 更新RouterOS配置
        sed -i "s/^ROUTEROS_HOST=.*/ROUTEROS_HOST=\"$routeros_host\"/" "$config_file"
        sed -i "s/^ROUTEROS_PORT=.*/ROUTEROS_PORT=\"$routeros_port\"/" "$config_file"
        sed -i "s/^ROUTEROS_USER=.*/ROUTEROS_USER=\"$routeros_user\"/" "$config_file"
        sed -i "s/^ROUTEROS_PASSWORD=.*/ROUTEROS_PASSWORD=\"$routeros_password\"/" "$config_file"
        
        log_info "RouterOS API配置完成！"
        log_info "配置已保存到: $config_file"
        
        # 测试获取IP
        echo ""
        log_info "测试获取WAN接口IP..."
        local wan_ip
        if wan_ip=$(get_routeros_wan_ip); then
            log_info "当前WAN接口IP: $wan_ip"
        else
            log_warn "无法获取WAN接口IP，请检查网络配置"
        fi
        
        return 0
    else
        log_error "连接测试失败！"
        log_info "请检查以下项目："
        log_info "  1. RouterOS主机地址是否正确"
        log_info "  2. API服务是否已启用"
        log_info "  3. 用户是否有API访问权限"
        log_info "  4. 网络连接是否正常"
        return 1
    fi
}

# 显示RouterOS API启用指南
show_api_enable_guide() {
    cat << EOF

RouterOS API启用指南
===================

要在RouterOS上启用API服务，请按以下步骤操作：

1. 通过WebFig或WinBox连接到RouterOS
2. 进入 IP -> Services
3. 找到 "api" 服务
4. 双击编辑，确保：
   - Disabled: 未勾选 (启用服务)
   - Port: 8728 (默认端口)
   - Available From: 0.0.0.0/0 (或限制为特定IP)

或者通过SSH/Terminal执行以下命令：

/ip service enable api
/ip service set api port=8728

创建API用户 (可选)：

/user add name=matrix-api group=full password=your_password
/user set matrix-api disabled=no

安全建议：
- 创建专门的API用户，不要使用admin账户
- 设置强密码
- 限制API服务的访问来源IP
- 定期更换密码

EOF
}

# 创建API用户指南
show_user_creation_guide() {
    cat << EOF

RouterOS API用户创建指南
========================

为了安全起见，建议创建专门的API用户而不是使用admin账户。

通过WebFig/WinBox：
1. 进入 System -> Users
2. 点击 "+" 添加新用户
3. 设置用户名: matrix-api
4. 设置密码: (强密码)
5. 选择组: full 或 read (根据需要)
6. 确保 Disabled 未勾选

通过SSH/Terminal：
/user add name=matrix-api group=full password=your_strong_password
/user set matrix-api disabled=no

权限说明：
- full: 完全访问权限 (推荐用于Matrix部署)
- read: 只读权限 (仅查看信息)
- write: 读写权限 (不包括敏感操作)

EOF
}

# 获取WAN接口IP
get_wan_ip() {
    log_info "获取RouterOS WAN接口IP"
    
    # 加载配置
    if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
        source "$PROJECT_DIR/config/deployment.env"
    fi
    
    if wan_ip=$(get_routeros_wan_ip); then
        echo "WAN接口IP: $wan_ip"
        return 0
    else
        log_error "无法获取WAN接口IP"
        return 1
    fi
}

# 列出网络接口
list_interfaces() {
    log_info "获取RouterOS网络接口列表"
    
    # 加载配置
    if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
        source "$PROJECT_DIR/config/deployment.env"
    fi
    
    echo "RouterOS网络接口："
    echo "=================="
    get_routeros_interfaces
}

# 列出IP地址
list_addresses() {
    log_info "获取RouterOS IP地址列表"
    
    # 加载配置
    if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
        source "$PROJECT_DIR/config/deployment.env"
    fi
    
    echo "RouterOS IP地址："
    echo "================="
    get_routeros_ip_addresses
}

# 测试连接
test_connection() {
    log_info "测试RouterOS API连接"
    
    # 加载配置
    if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
        source "$PROJECT_DIR/config/deployment.env"
    fi
    
    # 允许命令行参数覆盖配置
    if [[ -n "${CLI_HOST:-}" ]]; then
        export ROUTEROS_HOST="$CLI_HOST"
    fi
    if [[ -n "${CLI_PORT:-}" ]]; then
        export ROUTEROS_PORT="$CLI_PORT"
    fi
    if [[ -n "${CLI_USER:-}" ]]; then
        export ROUTEROS_USER="$CLI_USER"
    fi
    if [[ -n "${CLI_PASSWORD:-}" ]]; then
        export ROUTEROS_PASSWORD="$CLI_PASSWORD"
    fi
    
    if test_routeros_connection; then
        log_info "RouterOS API连接测试成功"
        
        # 尝试获取WAN IP
        if wan_ip=$(get_routeros_wan_ip); then
            log_info "WAN接口IP: $wan_ip"
        fi
        
        return 0
    else
        log_error "RouterOS API连接测试失败"
        return 1
    fi
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --host)
                CLI_HOST="$2"
                shift 2
                ;;
            --port)
                CLI_PORT="$2"
                shift 2
                ;;
            --user)
                CLI_USER="$2"
                shift 2
                ;;
            --password)
                CLI_PASSWORD="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local command="$1"
    shift
    
    # 解析命令行参数
    parse_arguments "$@"
    
    case "$command" in
        "configure")
            configure_routeros
            ;;
        "test")
            test_connection
            ;;
        "get-ip")
            get_wan_ip
            ;;
        "list-interfaces")
            list_interfaces
            ;;
        "list-addresses")
            list_addresses
            ;;
        "enable-api")
            show_api_enable_guide
            ;;
        "create-user")
            show_user_creation_guide
            ;;
        "--help"|"help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
