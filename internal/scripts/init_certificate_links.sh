#!/bin/bash

# ================================================================
# Matrix Homeserver 证书符号链接初始化脚本
# ================================================================
# 功能: 初始化证书符号链接，确保部署目录与acme.sh存储的连接
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载配置
if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
    source "$PROJECT_DIR/config/deployment.env"
else
    echo "错误: 配置文件 $PROJECT_DIR/config/deployment.env 不存在"
    exit 1
fi

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"

# 默认配置
LOG_FILE="${LOG_DIR}/init_certificate_links.log"
ACME_HOME=${ACME_HOME:-"/root/.acme.sh"}
ACME_DEPLOY_DIR=${ACME_DEPLOY_DIR:-"${DATA_DIR}/acme"}

# 初始化日志
init_logging "$LOG_FILE"

# 初始化证书符号链接
init_certificate_links() {
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    
    log_info "初始化证书符号链接: $domain"
    
    # 检查acme.sh目录是否存在
    if [[ ! -d "$ACME_HOME" ]]; then
        log_error "acme.sh目录不存在: $ACME_HOME"
        log_info "请先安装acme.sh或运行证书申请"
        return 1
    fi
    
    # 确定证书源目录
    local acme_source_dir=""
    if [[ -d "${ACME_HOME}/${domain}_ecc" ]]; then
        acme_source_dir="${ACME_HOME}/${domain}_ecc"
        log_info "找到ECC证书: $acme_source_dir"
    elif [[ -d "${ACME_HOME}/${domain}" ]]; then
        acme_source_dir="${ACME_HOME}/${domain}"
        log_info "找到RSA证书: $acme_source_dir"
    else
        log_warn "未找到域名 $domain 的证书目录"
        log_info "可用的证书目录:"
        ls -la "$ACME_HOME" | grep "^d" || log_info "  (无)"
        return 1
    fi
    
    # 检查证书文件是否存在
    local required_files=("fullchain.cer" "${domain}.key" "ca.cer")
    for file in "${required_files[@]}"; do
        if [[ ! -f "${acme_source_dir}/${file}" ]]; then
            log_error "证书文件不存在: ${acme_source_dir}/${file}"
            return 1
        fi
    done
    
    # 创建部署目录中的符号链接目录
    log_info "创建符号链接目录结构"
    ensure_dir "${ACME_DEPLOY_DIR}/${domain}" 755
    ensure_dir "${NGINX_CERT_DIR}" 755
    ensure_dir "${COTURN_CERT_DIR}" 755
    
    # 在部署目录中创建指向acme.sh存储的符号链接
    log_info "创建主要证书符号链接"
    ln -sf "${acme_source_dir}/fullchain.cer" "${ACME_DEPLOY_DIR}/${domain}/fullchain.cer"
    ln -sf "${acme_source_dir}/${domain}.key" "${ACME_DEPLOY_DIR}/${domain}/private.key"
    ln -sf "${acme_source_dir}/ca.cer" "${ACME_DEPLOY_DIR}/${domain}/ca.cer"
    
    # 创建服务专用的符号链接
    log_info "创建服务专用证书符号链接"
    
    # Nginx证书链接
    ln -sf "${ACME_DEPLOY_DIR}/${domain}/fullchain.cer" "${NGINX_CERT_DIR}/fullchain.cer"
    ln -sf "${ACME_DEPLOY_DIR}/${domain}/private.key" "${NGINX_CERT_DIR}/private.key"
    
    # Coturn证书链接
    ln -sf "${ACME_DEPLOY_DIR}/${domain}/fullchain.cer" "${COTURN_CERT_DIR}/fullchain.cer"
    ln -sf "${ACME_DEPLOY_DIR}/${domain}/private.key" "${COTURN_CERT_DIR}/private.key"
    
    # 验证符号链接
    log_info "验证证书符号链接"
    local links_valid=true
    
    local check_files=(
        "${ACME_DEPLOY_DIR}/${domain}/fullchain.cer"
        "${ACME_DEPLOY_DIR}/${domain}/private.key"
        "${NGINX_CERT_DIR}/fullchain.cer"
        "${NGINX_CERT_DIR}/private.key"
        "${COTURN_CERT_DIR}/fullchain.cer"
        "${COTURN_CERT_DIR}/private.key"
    )
    
    for link_file in "${check_files[@]}"; do
        if [[ -L "$link_file" ]] && [[ -f "$link_file" ]]; then
            local target=$(readlink "$link_file")
            log_debug "符号链接有效: $link_file -> $target"
        else
            log_error "符号链接无效: $link_file"
            links_valid=false
        fi
    done
    
    if [[ "$links_valid" == "true" ]]; then
        log_info "所有证书符号链接创建成功"
        
        # 显示证书信息
        show_certificate_info "$domain"
        
        return 0
    else
        log_error "部分证书符号链接创建失败"
        return 1
    fi
}

# 显示证书信息
show_certificate_info() {
    local domain="$1"
    local cert_file="${NGINX_CERT_DIR}/fullchain.cer"
    
    if [[ -f "$cert_file" ]]; then
        log_info "证书信息:"
        
        # 证书有效期
        local cert_dates
        cert_dates=$(openssl x509 -in "$cert_file" -noout -dates 2>/dev/null || echo "无法获取证书日期")
        log_info "  $cert_dates"
        
        # 证书主题
        local cert_subject
        cert_subject=$(openssl x509 -in "$cert_file" -noout -subject 2>/dev/null || echo "无法获取证书主题")
        log_info "  $cert_subject"
        
        # 证书颁发者
        local cert_issuer
        cert_issuer=$(openssl x509 -in "$cert_file" -noout -issuer 2>/dev/null || echo "无法获取证书颁发者")
        log_info "  $cert_issuer"
    else
        log_warn "无法读取证书文件进行信息显示"
    fi
}

# 清理无效的符号链接
cleanup_broken_links() {
    log_info "清理无效的符号链接"
    
    local dirs_to_check=(
        "${ACME_DEPLOY_DIR}"
        "${NGINX_CERT_DIR}"
        "${COTURN_CERT_DIR}"
    )
    
    for dir in "${dirs_to_check[@]}"; do
        if [[ -d "$dir" ]]; then
            find "$dir" -type l ! -exec test -e {} \; -delete 2>/dev/null || true
            log_debug "已清理目录中的无效符号链接: $dir"
        fi
    done
}

# 显示符号链接状态
show_link_status() {
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    
    log_info "证书符号链接状态:"
    
    echo "证书链路径:"
    echo "  acme.sh存储: ${ACME_HOME}/${domain}_ecc 或 ${ACME_HOME}/${domain}"
    echo "  ↓"
    echo "  部署目录: ${ACME_DEPLOY_DIR}/${domain}/"
    echo "  ↓"
    echo "  服务目录: ${NGINX_CERT_DIR}/ 和 ${COTURN_CERT_DIR}/"
    echo ""
    
    # 检查各级符号链接
    local files_to_check=(
        "${ACME_DEPLOY_DIR}/${domain}/fullchain.cer:部署目录证书"
        "${ACME_DEPLOY_DIR}/${domain}/private.key:部署目录私钥"
        "${NGINX_CERT_DIR}/fullchain.cer:Nginx证书"
        "${NGINX_CERT_DIR}/private.key:Nginx私钥"
        "${COTURN_CERT_DIR}/fullchain.cer:Coturn证书"
        "${COTURN_CERT_DIR}/private.key:Coturn私钥"
    )
    
    for file_desc in "${files_to_check[@]}"; do
        IFS=':' read -ra parts <<< "$file_desc"
        local file_path="${parts[0]}"
        local description="${parts[1]}"
        
        if [[ -L "$file_path" ]] && [[ -f "$file_path" ]]; then
            local target=$(readlink "$file_path")
            echo "✓ $description: $file_path -> $target"
        elif [[ -L "$file_path" ]]; then
            echo "✗ $description: $file_path (无效链接)"
        elif [[ -f "$file_path" ]]; then
            echo "! $description: $file_path (常规文件，非链接)"
        else
            echo "✗ $description: $file_path (不存在)"
        fi
    done
}

# 主函数
main() {
    local action="${1:-init}"
    
    case "$action" in
        "init")
            log_info "开始初始化证书符号链接"
            cleanup_broken_links
            if init_certificate_links; then
                log_info "证书符号链接初始化完成"
            else
                log_error "证书符号链接初始化失败"
                exit 1
            fi
            ;;
        "status")
            show_link_status
            ;;
        "cleanup")
            cleanup_broken_links
            log_info "无效符号链接清理完成"
            ;;
        *)
            echo "用法: $0 [init|status|cleanup]"
            echo "  init    - 初始化证书符号链接 (默认)"
            echo "  status  - 显示符号链接状态"
            echo "  cleanup - 清理无效的符号链接"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
