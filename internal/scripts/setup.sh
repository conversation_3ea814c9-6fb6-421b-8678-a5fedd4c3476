#!/bin/bash

# ================================================================
# Matrix Homeserver 初始化部署脚本
# ================================================================
# 功能: 自动化初始化Matrix Homeserver部署环境
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"

# 默认配置
LOG_FILE="${PROJECT_DIR}/setup.log"
FORCE_SETUP=false
SKIP_DEPS=false
SKIP_CONFIG=false
SKIP_CERTS=false

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE_SETUP=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-config)
                SKIP_CONFIG=true
                shift
                ;;
            --skip-certs)
                SKIP_CERTS=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 初始化部署脚本

用法: $0 [选项]

选项:
    --force         强制重新初始化，覆盖现有配置
    --skip-deps     跳过依赖检查和安装
    --skip-config   跳过配置文件生成
    --skip-certs    跳过证书申请
    --help          显示此帮助信息

示例:
    $0                    # 完整初始化
    $0 --force            # 强制重新初始化
    $0 --skip-deps        # 跳过依赖安装

EOF
}

# 检查运行环境
check_environment() {
    log_info "检查运行环境"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "不支持的操作系统"
        return 1
    fi
    
    local os_info
    os_info=$(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)
    log_info "操作系统: $os_info"
    
    # 检查用户权限
    if [[ $EUID -eq 0 ]]; then
        log_warn "不建议以root用户运行此脚本"
    fi
    
    # 检查Docker组权限
    if ! groups | grep -q docker; then
        log_error "当前用户不在docker组中，请运行: sudo usermod -aG docker $USER"
        return 1
    fi
    
    return 0
}

# 检查和安装依赖
check_dependencies() {
    if [[ "$SKIP_DEPS" == "true" ]]; then
        log_info "跳过依赖检查"
        return 0
    fi
    
    log_info "检查系统依赖"
    
    local required_commands=(
        "docker"
        "docker-compose"
        "curl"
        "openssl"
        "acme.sh"
        "python3"
    )
    
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        log_info "请安装缺少的依赖后重新运行"
        return 1
    fi
    
    # 检查Docker服务状态
    if ! service_running docker; then
        log_error "Docker服务未运行，请启动Docker服务"
        return 1
    fi
    
    # 检查Docker Compose版本
    local compose_version
    compose_version=$(docker-compose --version | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
    log_info "Docker Compose版本: $compose_version"
    
    # 检查acme.sh版本
    local acme_version
    acme_version=$(acme.sh --version 2>/dev/null | head -1 || echo "未知")
    log_info "acme.sh版本: $acme_version"

    # 检查Python3版本
    local python_version
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python3版本: $python_version"

    # 检查pip3 (可选)
    if command_exists pip3; then
        local pip_version
        pip_version=$(pip3 --version 2>&1 | cut -d' ' -f2)
        log_info "pip3版本: $pip_version"

        # 检查RouterOS API库 (可选)
        if python3 -c "import routeros_api" 2>/dev/null; then
            local api_version
            api_version=$(python3 -c "import routeros_api; print(routeros_api.__version__)" 2>/dev/null || echo "未知")
            log_info "RouterOS API版本: $api_version"
        else
            log_info "RouterOS API库未安装 (可选功能)"
        fi
    else
        log_info "pip3未安装 (RouterOS API功能不可用)"
    fi

    log_info "依赖检查完成"
    return 0
}

# 创建目录结构
create_directory_structure() {
    log_info "创建目录结构"
    
    local directories=(
        "${PROJECT_DIR}/data"
        "${PROJECT_DIR}/data/acme"
        "${PROJECT_DIR}/data/nginx/conf"
        "${PROJECT_DIR}/data/nginx/certs"
        "${PROJECT_DIR}/data/nginx/logs"
        "${PROJECT_DIR}/data/coturn/conf"
        "${PROJECT_DIR}/data/coturn/certs"
        "${PROJECT_DIR}/data/coturn/logs"
        "${PROJECT_DIR}/data/synapse"
        "${PROJECT_DIR}/data/postgres"
        "${PROJECT_DIR}/data/redis"
        "${PROJECT_DIR}/data/backup"
        "${PROJECT_DIR}/data/backup/certificates"
        "${PROJECT_DIR}/data/logs"
        "/var/log/matrix"
    )
    
    for dir in "${directories[@]}"; do
        if ensure_dir "$dir" 755; then
            log_debug "创建目录: $dir"
        else
            log_error "无法创建目录: $dir"
            return 1
        fi
    done
    
    # 设置特殊权限
    chmod 755 "${PROJECT_DIR}/data/acme"  # 符号链接目录，需要可读
    chmod 700 "${PROJECT_DIR}/data/backup"

    # 确保acme.sh默认目录存在且有正确权限
    if [[ ! -d "/root/.acme.sh" ]]; then
        mkdir -p "/root/.acme.sh"
        chmod 700 "/root/.acme.sh"
    fi
    
    log_info "目录结构创建完成"
    return 0
}

# 加载配置文件
load_configuration() {
    local config_file="${PROJECT_DIR}/config/deployment.env"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "请复制模板文件并编辑配置:"
        log_info "  cp config/deployment.env.template config/deployment.env"
        log_info "  nano config/deployment.env"
        return 1
    fi
    
    source "$config_file"
    
    # 验证必需配置
    local required_vars=(
        "DOMAIN"
        "SUBDOMAIN_MATRIX"
        "HTTPS_PORT"
        "DB_PASSWORD"
        "CLOUDFLARE_API_TOKEN"
        "CLOUDFLARE_ZONE_ID"
        "COTURN_SHARED_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "必需配置项未设置: $var"
            return 1
        fi
    done
    
    log_info "配置文件加载完成"
    return 0
}

# 生成配置文件
generate_configuration_files() {
    if [[ "$SKIP_CONFIG" == "true" ]]; then
        log_info "跳过配置文件生成"
        return 0
    fi
    
    log_info "生成配置文件"
    
    # 生成Nginx配置
    generate_nginx_config
    
    # 生成Synapse配置
    generate_synapse_config
    
    # 生成Coturn配置
    generate_coturn_config
    
    # 生成PostgreSQL初始化脚本
    generate_postgres_config
    
    log_info "配置文件生成完成"
    return 0
}

# 生成Nginx配置
generate_nginx_config() {
    log_debug "生成Nginx配置"
    
    # 主配置文件
    envsubst < "${PROJECT_DIR}/config/nginx.conf.template" > "${PROJECT_DIR}/data/nginx/conf/nginx.conf"
    
    # 站点配置文件
    envsubst < "${PROJECT_DIR}/config/matrix.conf.template" > "${PROJECT_DIR}/data/nginx/conf/matrix.conf"
    
    log_debug "Nginx配置生成完成"
}

# 生成Synapse配置
generate_synapse_config() {
    log_debug "生成Synapse配置"
    
    # 生成随机密钥
    export SYNAPSE_REGISTRATION_SECRET=$(generate_random_string 64)
    export SYNAPSE_FORM_SECRET=$(generate_random_string 64)
    export SYNAPSE_MACAROON_SECRET=$(generate_random_string 64)
    
    # 生成配置文件
    envsubst < "${PROJECT_DIR}/config/homeserver.yaml.template" > "${PROJECT_DIR}/data/synapse/homeserver.yaml"
    
    # 生成日志配置
    cat > "${PROJECT_DIR}/data/synapse/log.config" << 'EOF'
version: 1

formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'

handlers:
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver.log
    when: midnight
    backupCount: 3
    encoding: utf8

  console:
    class: logging.StreamHandler
    formatter: precise

loggers:
    synapse.storage.SQL:
        level: INFO

root:
    level: INFO
    handlers: [file, console]

disable_existing_loggers: false
EOF
    
    log_debug "Synapse配置生成完成"
}

# 生成Coturn配置
generate_coturn_config() {
    log_debug "生成Coturn配置"
    
    # 替换占位符
    sed -e "s/PLACEHOLDER_EXTERNAL_IP/127.0.0.1/" \
        -e "s/PLACEHOLDER_MIN_PORT/${COTURN_MIN_PORT}/" \
        -e "s/PLACEHOLDER_MAX_PORT/${COTURN_MAX_PORT}/" \
        "${PROJECT_DIR}/config/turnserver.conf.template" | \
        envsubst > "${PROJECT_DIR}/data/coturn/conf/turnserver.conf"
    
    log_debug "Coturn配置生成完成"
}

# 生成PostgreSQL配置
generate_postgres_config() {
    log_debug "生成PostgreSQL初始化脚本"
    
    cat > "${PROJECT_DIR}/config/postgres-init.sql" << EOF
-- Matrix Homeserver PostgreSQL初始化脚本
-- 设置数据库编码和排序规则
ALTER DATABASE ${DB_NAME} SET default_text_search_config = 'pg_catalog.english';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 优化配置
ALTER SYSTEM SET shared_buffers = '${POSTGRES_SHARED_BUFFERS:-256MB}';
ALTER SYSTEM SET effective_cache_size = '${POSTGRES_EFFECTIVE_CACHE_SIZE:-1GB}';
ALTER SYSTEM SET work_mem = '${POSTGRES_WORK_MEM:-4MB}';
ALTER SYSTEM SET maintenance_work_mem = '${POSTGRES_MAINTENANCE_WORK_MEM:-64MB}';

-- 重新加载配置
SELECT pg_reload_conf();
EOF
    
    log_debug "PostgreSQL配置生成完成"
}

# 初始化SSL证书
initialize_certificates() {
    if [[ "$SKIP_CERTS" == "true" ]]; then
        log_info "跳过证书初始化"
        return 0
    fi

    log_info "初始化SSL证书"

    # 运行证书管理脚本
    if "${SCRIPT_DIR}/certificate_manager.sh"; then
        log_info "证书申请/续期完成"

        # 初始化证书符号链接
        log_info "初始化证书符号链接结构"
        if "${SCRIPT_DIR}/init_certificate_links.sh" init; then
            log_info "证书符号链接初始化完成"

            # 验证证书链接状态
            log_info "验证证书符号链接状态"
            "${SCRIPT_DIR}/init_certificate_links.sh" status
        else
            log_error "证书符号链接初始化失败"
            log_info "尝试手动修复: ${SCRIPT_DIR}/init_certificate_links.sh init"
            return 1
        fi

        return 0
    else
        log_error "证书初始化失败"
        return 1
    fi
}

# 初始化IP同步
initialize_ip_sync() {
    log_info "初始化IP同步"
    
    # 运行IP监控脚本
    if "${SCRIPT_DIR}/ip_watchdog.sh" --force-update; then
        log_info "IP同步初始化完成"
        return 0
    else
        log_error "IP同步初始化失败"
        return 1
    fi
}

# 启动服务
start_services() {
    log_info "启动Matrix服务"
    
    cd "$PROJECT_DIR"
    
    # 拉取最新镜像
    if docker-compose pull; then
        log_info "镜像拉取完成"
    else
        log_warn "镜像拉取失败，使用本地镜像"
    fi
    
    # 启动服务
    if docker-compose up -d; then
        log_info "服务启动完成"
        
        # 等待服务启动
        sleep 10
        
        # 检查服务状态
        check_services_health
        
        return 0
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态"
    
    local services=("db" "redis" "synapse" "nginx" "coturn")
    local healthy_count=0
    
    for service in "${services[@]}"; do
        if container_running "matrix_${service}"; then
            log_info "服务运行正常: $service"
            healthy_count=$((healthy_count + 1))
        else
            log_warn "服务状态异常: $service"
        fi
    done
    
    log_info "健康检查完成: $healthy_count/${#services[@]} 服务正常"
    
    if [[ $healthy_count -eq ${#services[@]} ]]; then
        return 0
    else
        return 1
    fi
}

# 设置定时任务
setup_cron_jobs() {
    log_info "设置定时任务"
    
    # 检查是否已存在定时任务
    if crontab -l 2>/dev/null | grep -q "matrix"; then
        if [[ "$FORCE_SETUP" != "true" ]]; then
            log_info "定时任务已存在，跳过设置"
            return 0
        else
            log_info "强制模式，重新设置定时任务"
        fi
    fi
    
    # 创建临时crontab文件
    local temp_cron
    temp_cron=$(mktemp)
    
    # 保留现有的非matrix相关任务
    crontab -l 2>/dev/null | grep -v "matrix" > "$temp_cron" || true
    
    # 添加matrix相关任务
    cat >> "$temp_cron" << EOF
# Matrix Homeserver自动化任务
# IP监控 - 每分钟执行
* * * * * ${SCRIPT_DIR}/ip_watchdog.sh >> /var/log/matrix/ip_watchdog.log 2>&1

# 证书管理 - 每天凌晨2点执行
0 2 * * * ${SCRIPT_DIR}/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1

# 健康检查 - 每5分钟执行
*/5 * * * * ${SCRIPT_DIR}/health_check.sh >> /var/log/matrix/health_check.log 2>&1

# 备份 - 每天凌晨3点执行
0 3 * * * ${SCRIPT_DIR}/backup.sh >> /var/log/matrix/backup.log 2>&1
EOF
    
    # 安装新的crontab
    if crontab "$temp_cron"; then
        log_info "定时任务设置完成"
        rm -f "$temp_cron"
        return 0
    else
        log_error "定时任务设置失败"
        rm -f "$temp_cron"
        return 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    
    cat << EOF

=================================================================
Matrix Homeserver 部署完成
=================================================================

服务信息:
  域名: ${DOMAIN}
  Matrix服务: https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}
  管理员用户: @${SYNAPSE_ADMIN_USER:-admin}:${DOMAIN}

服务状态:
$(docker-compose ps)

下一步操作:
1. 创建管理员用户:
   docker-compose exec synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008

2. 测试服务:
   curl -k https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}/_matrix/client/versions

3. 查看日志:
   docker-compose logs -f

4. 监控脚本:
   tail -f /var/log/matrix/*.log

配置文件位置:
  主配置: ${PROJECT_DIR}/config/deployment.env
  数据目录: ${PROJECT_DIR}/data/
  日志目录: /var/log/matrix/

=================================================================

EOF
}

# 主要处理逻辑
main() {
    log_info "开始Matrix Homeserver初始化部署"
    
    # 检查运行环境
    if ! check_environment; then
        log_fatal "环境检查失败"
    fi
    
    # 检查依赖
    if ! check_dependencies; then
        log_fatal "依赖检查失败"
    fi
    
    # 创建目录结构
    if ! create_directory_structure; then
        log_fatal "目录创建失败"
    fi
    
    # 加载配置
    if ! load_configuration; then
        log_fatal "配置加载失败"
    fi
    
    # 生成配置文件
    if ! generate_configuration_files; then
        log_fatal "配置文件生成失败"
    fi
    
    # 初始化证书
    if ! initialize_certificates; then
        log_fatal "证书初始化失败"
    fi
    
    # 初始化IP同步
    if ! initialize_ip_sync; then
        log_fatal "IP同步初始化失败"
    fi
    
    # 启动服务
    if ! start_services; then
        log_fatal "服务启动失败"
    fi
    
    # 设置定时任务
    if ! setup_cron_jobs; then
        log_warn "定时任务设置失败，请手动设置"
    fi
    
    # 显示部署信息
    show_deployment_info
    
    log_info "Matrix Homeserver初始化部署完成"
}

# 解析参数并执行
parse_arguments "$@"

# 初始化日志
init_logging "$LOG_FILE"

# 执行主逻辑
main
