#!/bin/bash

# ================================================================
# Matrix Homeserver 管理员脚本
# ================================================================
# 功能: 提供Matrix Homeserver的用户管理、房间管理和服务器管理功能
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载配置
if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
    source "$PROJECT_DIR/config/deployment.env"
else
    echo "错误: 配置文件 $PROJECT_DIR/config/deployment.env 不存在"
    exit 1
fi

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"

# 默认配置
LOG_FILE="${LOG_DIR}/admin.log"
SYNAPSE_CONTAINER="matrix_synapse"
SYNAPSE_CONFIG="/data/homeserver.yaml"
SYNAPSE_URL="http://localhost:8008"

# 初始化日志
init_logging "$LOG_FILE"

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 管理员脚本

用法: $0 <命令> [选项]

用户管理:
    user create <username> [password]     创建用户
    user delete <username>                删除用户
    user list                            列出所有用户
    user info <username>                 显示用户信息
    user activate <username>             激活用户
    user deactivate <username>           停用用户
    user reset-password <username>       重置用户密码
    user make-admin <username>           设置用户为管理员
    user remove-admin <username>         移除用户管理员权限

房间管理:
    room list                           列出所有房间
    room info <room_id>                 显示房间信息
    room delete <room_id>               删除房间
    room members <room_id>              显示房间成员
    room purge-history <room_id> <days> 清理房间历史记录

服务器管理:
    server status                       显示服务器状态
    server stats                        显示服务器统计信息
    server version                      显示服务器版本
    server shutdown                     关闭服务器
    server restart                      重启服务器
    server backup                       备份服务器数据
    server maintenance <on|off>         维护模式开关

数据库管理:
    db vacuum                          数据库清理
    db stats                           数据库统计
    db backup                          数据库备份
    db check                           数据库检查

选项:
    --help                             显示此帮助信息
    --verbose                          显示详细输出
    --dry-run                          仅显示将要执行的操作

示例:
    $0 user create alice                # 创建用户alice
    $0 user make-admin alice            # 设置alice为管理员
    $0 room list                        # 列出所有房间
    $0 server stats                     # 显示服务器统计

EOF
}

# 检查Synapse容器状态
check_synapse_container() {
    if ! container_running "$SYNAPSE_CONTAINER"; then
        log_error "Synapse容器未运行: $SYNAPSE_CONTAINER"
        log_info "请先启动Matrix服务: docker compose up -d"
        return 1
    fi
    return 0
}

# 执行Synapse管理命令
execute_synapse_admin() {
    local command="$1"
    shift
    
    if ! check_synapse_container; then
        return 1
    fi
    
    log_debug "执行Synapse管理命令: $command $*"
    
    case "$command" in
        "register_new_matrix_user")
            docker compose -f "$PROJECT_DIR/docker-compose.yml" exec "$SYNAPSE_CONTAINER" \
                register_new_matrix_user -c "$SYNAPSE_CONFIG" "$SYNAPSE_URL" "$@"
            ;;
        "synapse_admin")
            docker compose -f "$PROJECT_DIR/docker-compose.yml" exec "$SYNAPSE_CONTAINER" \
                python -m synapse.app.admin_cmd -c "$SYNAPSE_CONFIG" "$@"
            ;;
        "psql")
            docker compose -f "$PROJECT_DIR/docker-compose.yml" exec db \
                psql -U "${DB_USER}" -d "${DB_NAME}" "$@"
            ;;
        *)
            docker compose -f "$PROJECT_DIR/docker-compose.yml" exec "$SYNAPSE_CONTAINER" \
                "$command" "$@"
            ;;
    esac
}

# 用户管理函数
user_create() {
    local username="$1"
    local password="${2:-}"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    # 验证用户名格式
    if [[ ! "$username" =~ ^[a-z0-9._=-]+$ ]]; then
        log_error "用户名格式无效: $username"
        log_info "用户名只能包含小写字母、数字、点、下划线、等号和连字符"
        return 1
    fi
    
    log_info "创建用户: $username"
    
    if [[ -n "$password" ]]; then
        # 使用提供的密码
        execute_synapse_admin "register_new_matrix_user" -u "$username" -p "$password"
    else
        # 交互式创建
        execute_synapse_admin "register_new_matrix_user" -u "$username"
    fi
    
    if [[ $? -eq 0 ]]; then
        log_info "用户创建成功: @${username}:${DOMAIN}"
    else
        log_error "用户创建失败: $username"
        return 1
    fi
}

user_delete() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_warn "删除用户: $username"
    log_warn "此操作不可逆，将删除用户的所有数据"
    
    read -p "确认删除用户 $username? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    # 使用SQL直接删除用户
    local sql="UPDATE users SET deactivated = 1 WHERE name = '@${username}:${DOMAIN}';"
    
    if execute_synapse_admin "psql" -c "$sql"; then
        log_info "用户已停用: $username"
        log_info "注意: 用户数据仍在数据库中，如需完全删除请联系管理员"
    else
        log_error "用户删除失败: $username"
        return 1
    fi
}

user_list() {
    log_info "获取用户列表"
    
    local sql="SELECT name, admin, deactivated, creation_ts FROM users ORDER BY creation_ts DESC;"
    
    execute_synapse_admin "psql" -c "$sql"
}

user_info() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_info "获取用户信息: $username"
    
    local sql="SELECT name, admin, deactivated, creation_ts, last_seen FROM users WHERE name = '@${username}:${DOMAIN}';"
    
    execute_synapse_admin "psql" -c "$sql"
}

user_make_admin() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_info "设置用户为管理员: $username"
    
    local sql="UPDATE users SET admin = 1 WHERE name = '@${username}:${DOMAIN}';"
    
    if execute_synapse_admin "psql" -c "$sql"; then
        log_info "用户已设置为管理员: $username"
    else
        log_error "设置管理员失败: $username"
        return 1
    fi
}

user_remove_admin() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_info "移除用户管理员权限: $username"
    
    local sql="UPDATE users SET admin = 0 WHERE name = '@${username}:${DOMAIN}';"
    
    if execute_synapse_admin "psql" -c "$sql"; then
        log_info "用户管理员权限已移除: $username"
    else
        log_error "移除管理员权限失败: $username"
        return 1
    fi
}

user_reset_password() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_info "重置用户密码: $username"
    
    # 生成随机密码
    local new_password
    new_password=$(generate_random_string 16)
    
    # 使用hash_password工具生成密码哈希
    local password_hash
    password_hash=$(docker compose -f "$PROJECT_DIR/docker-compose.yml" exec "$SYNAPSE_CONTAINER" \
        python -c "from synapse.util.caches.descriptors import cached; from synapse.handlers.auth import AuthHandler; import bcrypt; print(bcrypt.hashpw('$new_password'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8'))")
    
    if [[ -n "$password_hash" ]]; then
        local sql="UPDATE users SET password_hash = '$password_hash' WHERE name = '@${username}:${DOMAIN}';"
        
        if execute_synapse_admin "psql" -c "$sql"; then
            log_info "密码重置成功: $username"
            log_info "新密码: $new_password"
            log_warn "请立即将新密码告知用户并要求其修改"
        else
            log_error "密码重置失败: $username"
            return 1
        fi
    else
        log_error "密码哈希生成失败"
        return 1
    fi
}

# 房间管理函数
room_list() {
    log_info "获取房间列表"
    
    local sql="SELECT room_id, creator, is_public, join_rules, history_visibility FROM rooms ORDER BY room_id;"
    
    execute_synapse_admin "psql" -c "$sql"
}

room_info() {
    local room_id="$1"
    
    if [[ -z "$room_id" ]]; then
        log_error "房间ID不能为空"
        return 1
    fi
    
    log_info "获取房间信息: $room_id"
    
    local sql="SELECT room_id, creator, is_public, join_rules, history_visibility FROM rooms WHERE room_id = '$room_id';"
    
    execute_synapse_admin "psql" -c "$sql"
}

room_members() {
    local room_id="$1"
    
    if [[ -z "$room_id" ]]; then
        log_error "房间ID不能为空"
        return 1
    fi
    
    log_info "获取房间成员: $room_id"
    
    local sql="SELECT user_id, membership FROM room_memberships WHERE room_id = '$room_id' AND forgotten = 0;"
    
    execute_synapse_admin "psql" -c "$sql"
}

# 服务器管理函数
server_status() {
    log_info "检查服务器状态"
    
    echo "=== Docker容器状态 ==="
    docker compose -f "$PROJECT_DIR/docker-compose.yml" ps
    
    echo ""
    echo "=== Synapse版本信息 ==="
    if check_synapse_container; then
        execute_synapse_admin "python" -c "import synapse; print(f'Synapse版本: {synapse.__version__}')"
    fi
    
    echo ""
    echo "=== 系统资源使用 ==="
    echo "内存使用:"
    free -h
    echo ""
    echo "磁盘使用:"
    df -h "$PROJECT_DIR"
    echo ""
    echo "系统负载:"
    uptime
}

server_stats() {
    log_info "获取服务器统计信息"
    
    if ! check_synapse_container; then
        return 1
    fi
    
    echo "=== 用户统计 ==="
    local sql="SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN deactivated = 0 THEN 1 END) as active_users,
        COUNT(CASE WHEN admin = 1 THEN 1 END) as admin_users
        FROM users;"
    execute_synapse_admin "psql" -c "$sql"
    
    echo ""
    echo "=== 房间统计 ==="
    local sql="SELECT 
        COUNT(*) as total_rooms,
        COUNT(CASE WHEN is_public = true THEN 1 END) as public_rooms
        FROM rooms;"
    execute_synapse_admin "psql" -c "$sql"
    
    echo ""
    echo "=== 消息统计 ==="
    local sql="SELECT COUNT(*) as total_events FROM events;"
    execute_synapse_admin "psql" -c "$sql"
}

# 数据库管理函数
db_vacuum() {
    log_info "执行数据库清理"
    
    log_warn "数据库清理可能需要较长时间，建议在维护窗口执行"
    read -p "确认执行数据库清理? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    execute_synapse_admin "psql" -c "VACUUM ANALYZE;"
    
    log_info "数据库清理完成"
}

db_backup() {
    log_info "执行数据库备份"
    
    local backup_file="${PROJECT_DIR}/data/backup/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    ensure_dir "$(dirname "$backup_file")" 700
    
    if docker compose -f "$PROJECT_DIR/docker-compose.yml" exec db \
        pg_dump -U "${DB_USER}" "${DB_NAME}" > "$backup_file"; then
        log_info "数据库备份完成: $backup_file"
        
        # 压缩备份文件
        gzip "$backup_file"
        log_info "备份文件已压缩: ${backup_file}.gz"
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        "user")
            if [[ $# -eq 0 ]]; then
                log_error "用户管理命令需要子命令"
                show_help
                exit 1
            fi
            
            local subcommand="$1"
            shift
            
            case "$subcommand" in
                "create") user_create "$@" ;;
                "delete") user_delete "$@" ;;
                "list") user_list "$@" ;;
                "info") user_info "$@" ;;
                "make-admin") user_make_admin "$@" ;;
                "remove-admin") user_remove_admin "$@" ;;
                "reset-password") user_reset_password "$@" ;;
                *) log_error "未知的用户管理命令: $subcommand"; show_help; exit 1 ;;
            esac
            ;;
        "room")
            if [[ $# -eq 0 ]]; then
                log_error "房间管理命令需要子命令"
                show_help
                exit 1
            fi
            
            local subcommand="$1"
            shift
            
            case "$subcommand" in
                "list") room_list "$@" ;;
                "info") room_info "$@" ;;
                "members") room_members "$@" ;;
                *) log_error "未知的房间管理命令: $subcommand"; show_help; exit 1 ;;
            esac
            ;;
        "server")
            if [[ $# -eq 0 ]]; then
                log_error "服务器管理命令需要子命令"
                show_help
                exit 1
            fi
            
            local subcommand="$1"
            shift
            
            case "$subcommand" in
                "status") server_status "$@" ;;
                "stats") server_stats "$@" ;;
                "restart") 
                    log_info "重启Matrix服务"
                    docker compose -f "$PROJECT_DIR/docker-compose.yml" restart
                    ;;
                *) log_error "未知的服务器管理命令: $subcommand"; show_help; exit 1 ;;
            esac
            ;;
        "db")
            if [[ $# -eq 0 ]]; then
                log_error "数据库管理命令需要子命令"
                show_help
                exit 1
            fi
            
            local subcommand="$1"
            shift
            
            case "$subcommand" in
                "vacuum") db_vacuum "$@" ;;
                "backup") db_backup "$@" ;;
                *) log_error "未知的数据库管理命令: $subcommand"; show_help; exit 1 ;;
            esac
            ;;
        "--help"|"help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
