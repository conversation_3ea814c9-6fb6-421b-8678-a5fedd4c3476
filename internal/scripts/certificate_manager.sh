#!/bin/bash

# ================================================================
# Matrix Homeserver 证书管理脚本
# ================================================================
# 功能: 智能管理SSL证书的申请、续期和部署
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载配置
if [[ -f "$PROJECT_DIR/config/deployment.env" ]]; then
    source "$PROJECT_DIR/config/deployment.env"
else
    echo "错误: 配置文件 $PROJECT_DIR/config/deployment.env 不存在"
    exit 1
fi

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/logging.sh"

# 默认配置
LOG_FILE="${LOG_DIR}/certificate_manager.log"
LOCK_FILE="/tmp/certificate_manager.lock"
RENEWAL_THRESHOLD=${CERT_RENEWAL_THRESHOLD:-14}
ACME_HOME=${ACME_HOME:-"${DATA_DIR}/acme"}

# 命令行参数
FORCE_RENEW=false
CHECK_ONLY=false
CHECK_EXPIRY=false
VERBOSE=false

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-renew)
                FORCE_RENEW=true
                shift
                ;;
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            --check-expiry)
                CHECK_EXPIRY=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 证书管理脚本

用法: $0 [选项]

选项:
    --force-renew     强制续期证书，即使未到期
    --check-only      仅检查证书状态，不执行续期操作
    --check-expiry    检查证书到期时间
    --verbose         显示详细输出
    --help            显示此帮助信息

示例:
    $0                      # 正常运行，检查并续期证书
    $0 --check-only         # 仅检查证书状态
    $0 --force-renew        # 强制续期所有证书
    $0 --check-expiry       # 检查证书到期时间

EOF
}

# 创建锁文件
create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local lock_pid
        lock_pid=$(cat "$LOCK_FILE" 2>/dev/null || echo "")
        if [[ -n "$lock_pid" ]] && kill -0 "$lock_pid" 2>/dev/null; then
            log_error "脚本已在运行 (PID: $lock_pid)"
            exit 1
        else
            log_warn "发现过期的锁文件，正在清理"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# 清理锁文件
cleanup_lock() {
    rm -f "$LOCK_FILE"
}

# 检查证书有效期
check_certificate_validity() {
    local domain="$1"
    local cert_file="${ACME_HOME}/${domain}/fullchain.cer"
    
    if [[ ! -f "$cert_file" ]]; then
        echo "MISSING"
        return
    fi
    
    local expiry_date
    if ! expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate 2>/dev/null | cut -d= -f2); then
        echo "INVALID"
        return
    fi
    
    local expiry_epoch current_epoch days_left
    expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
    current_epoch=$(date +%s)
    days_left=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    echo "$days_left"
}

# 初始化acme.sh
init_acme() {
    log_info "初始化acme.sh环境"
    
    # 设置acme.sh环境变量
    export CF_Token="${CLOUDFLARE_API_TOKEN}"
    export CF_Zone_ID="${CLOUDFLARE_ZONE_ID}"
    
    # 检查acme.sh是否已安装
    if ! command -v acme.sh &> /dev/null; then
        log_error "acme.sh未安装，请先安装acme.sh"
        return 1
    fi
    
    # 设置默认CA
    acme.sh --set-default-ca --server letsencrypt
    
    log_info "acme.sh环境初始化完成"
}

# 申请或续期证书
issue_certificate() {
    local domain="$1"
    
    log_info "开始为域名 $domain 申请/续期证书"
    
    # 设置acme.sh参数
    local acme_args=(
        --issue
        --dns dns_cf
        -d "$domain"
        --cert-home "$ACME_HOME"
        --key-file "${ACME_HOME}/${domain}/${domain}.key"
        --fullchain-file "${ACME_HOME}/${domain}/fullchain.cer"
        --ca-file "${ACME_HOME}/${domain}/ca.cer"
    )
    
    if [[ "$FORCE_RENEW" == "true" ]]; then
        acme_args+=(--force)
    fi
    
    # 执行证书申请/续期
    if acme.sh "${acme_args[@]}"; then
        log_info "证书申请/续期成功: $domain"
        return 0
    else
        log_error "证书申请/续期失败: $domain"
        return 1
    fi
}

# 部署证书
deploy_certificate() {
    local domain="$1"
    local acme_dir="${ACME_HOME}/${domain}"
    
    log_info "开始部署证书: $domain"
    
    # 检查证书文件是否存在
    if [[ ! -f "${acme_dir}/fullchain.cer" ]] || [[ ! -f "${acme_dir}/${domain}.key" ]]; then
        log_error "证书文件不存在: $acme_dir"
        return 1
    fi
    
    # 创建证书软链接目录
    mkdir -p "${NGINX_CERT_DIR}" "${COTURN_CERT_DIR}"
    
    # 原子化更新Nginx证书软链接
    ln -sf "${acme_dir}/fullchain.cer" "${NGINX_CERT_DIR}/fullchain.cer.new"
    ln -sf "${acme_dir}/${domain}.key" "${NGINX_CERT_DIR}/private.key.new"
    
    mv "${NGINX_CERT_DIR}/fullchain.cer.new" "${NGINX_CERT_DIR}/fullchain.cer"
    mv "${NGINX_CERT_DIR}/private.key.new" "${NGINX_CERT_DIR}/private.key"
    
    # 原子化更新Coturn证书软链接
    ln -sf "${acme_dir}/fullchain.cer" "${COTURN_CERT_DIR}/fullchain.cer.new"
    ln -sf "${acme_dir}/${domain}.key" "${COTURN_CERT_DIR}/private.key.new"
    
    mv "${COTURN_CERT_DIR}/fullchain.cer.new" "${COTURN_CERT_DIR}/fullchain.cer"
    mv "${COTURN_CERT_DIR}/private.key.new" "${COTURN_CERT_DIR}/private.key"
    
    log_info "证书软链接更新完成: $domain"
    return 0
}

# 重载服务
reload_services() {
    log_info "开始重载相关服务"
    
    local reload_success=true
    
    # 重载Nginx
    if docker compose -f "$PROJECT_DIR/docker-compose.yml" exec nginx nginx -s reload 2>/dev/null; then
        log_info "Nginx重载成功"
    else
        log_warn "Nginx重载失败，尝试重启"
        if docker compose -f "$PROJECT_DIR/docker-compose.yml" restart nginx; then
            log_info "Nginx重启成功"
        else
            log_error "Nginx重启失败"
            reload_success=false
        fi
    fi
    
    # 重启Coturn (Coturn不支持热重载)
    if docker compose -f "$PROJECT_DIR/docker-compose.yml" restart coturn; then
        log_info "Coturn重启成功"
    else
        log_error "Coturn重启失败"
        reload_success=false
    fi
    
    if [[ "$reload_success" == "true" ]]; then
        log_info "服务重载完成"
        return 0
    else
        log_error "服务重载失败"
        return 1
    fi
}

# 验证证书部署
verify_certificate() {
    local domain="$1"
    local port="${HTTPS_PORT}"
    
    log_info "验证证书部署: $domain:$port"
    
    # 等待服务启动
    sleep 10
    
    # 检查证书
    local cert_info
    if cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:$port" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null); then
        log_info "证书验证成功"
        log_info "证书信息: $cert_info"
        return 0
    else
        log_warn "证书验证失败，但继续执行"
        return 1
    fi
}

# 备份证书
backup_certificate() {
    local domain="$1"
    local backup_dir="${DATA_DIR}/backup/certificates"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    log_info "备份证书: $domain"
    
    mkdir -p "$backup_dir"
    
    if tar -czf "${backup_dir}/cert_${domain}_${timestamp}.tar.gz" -C "$ACME_HOME" "$domain"; then
        log_info "证书备份成功: ${backup_dir}/cert_${domain}_${timestamp}.tar.gz"
        
        # 清理旧备份 (保留最近10个)
        find "$backup_dir" -name "cert_${domain}_*.tar.gz" -type f | sort -r | tail -n +11 | xargs -r rm -f
        
        return 0
    else
        log_error "证书备份失败"
        return 1
    fi
}

# 处理单个域名的证书
process_domain_certificate() {
    local domain="$1"
    local days_left
    
    log_info "处理域名证书: $domain"
    
    # 检查证书有效期
    days_left=$(check_certificate_validity "$domain")
    
    case "$days_left" in
        "MISSING")
            log_info "证书不存在，需要申请新证书: $domain"
            ;;
        "INVALID")
            log_warn "证书文件无效，需要重新申请: $domain"
            ;;
        *)
            if [[ "$days_left" -lt 0 ]]; then
                log_warn "证书已过期 ${days_left#-} 天: $domain"
            elif [[ "$days_left" -lt "$RENEWAL_THRESHOLD" ]]; then
                log_info "证书将在 $days_left 天后过期，需要续期: $domain"
            else
                log_info "证书有效期还有 $days_left 天: $domain"
                if [[ "$FORCE_RENEW" != "true" ]]; then
                    return 0
                fi
            fi
            ;;
    esac
    
    # 仅检查模式
    if [[ "$CHECK_ONLY" == "true" ]]; then
        return 0
    fi
    
    # 申请或续期证书
    if issue_certificate "$domain"; then
        # 备份证书
        backup_certificate "$domain"
        
        # 部署证书
        if deploy_certificate "$domain"; then
            # 重载服务
            if reload_services; then
                # 验证证书
                verify_certificate "$domain"
                log_info "域名 $domain 的证书处理完成"
                return 0
            fi
        fi
    fi
    
    log_error "域名 $domain 的证书处理失败"
    return 1
}

# 主要处理逻辑
main() {
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    
    log_info "开始证书管理任务"
    
    # 仅检查到期时间
    if [[ "$CHECK_EXPIRY" == "true" ]]; then
        local days_left
        days_left=$(check_certificate_validity "$domain")
        case "$days_left" in
            "MISSING")
                log_info "证书状态: 不存在"
                ;;
            "INVALID")
                log_info "证书状态: 无效"
                ;;
            *)
                if [[ "$days_left" -lt 0 ]]; then
                    log_info "证书状态: 已过期 ${days_left#-} 天"
                else
                    log_info "证书状态: 还有 $days_left 天到期"
                fi
                ;;
        esac
        exit 0
    fi
    
    # 初始化acme.sh
    if ! init_acme; then
        log_error "acme.sh初始化失败"
        exit 1
    fi
    
    # 处理域名证书
    if process_domain_certificate "$domain"; then
        log_info "证书管理任务完成"
    else
        log_error "证书管理任务失败"
        exit 1
    fi
}

# 信号处理
trap cleanup_lock EXIT
trap 'log_error "脚本被中断"; exit 1' INT TERM

# 解析参数并执行
parse_arguments "$@"

# 初始化
init_logging "$LOG_FILE"
create_lock

# 执行主逻辑
main
