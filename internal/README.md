# Matrix Homeserver 内部核心服务部署包

## 概述

本部署包包含Matrix Homeserver的所有核心服务，运行在动态IP的内网服务器上。包括Synapse、PostgreSQL、Redis、Coturn和Nginx等服务，以及自动化的IP监控和证书管理脚本。

## 系统要求

### 硬件要求
- **CPU**: 4核心（推荐8核心）
- **内存**: 8GB（推荐16GB）
- **存储**: 100GB SSD（推荐200GB以上）
- **网络**: 动态公网IP，支持端口转发

### 软件要求
- **操作系统**: Ubuntu 20.04 LTS或更新版本
- **Docker**: 20.10或更新版本
- **Docker Compose**: 2.0或更新版本
- **acme.sh**: 最新版本

### 网络要求
- **路由器**: 支持NAT端口转发配置
- **带宽**: 上行带宽至少10Mbps（推荐50Mbps以上）
- **域名**: 已配置的子域名和Cloudflare DNS管理

## 快速部署

### 1. 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装acme.sh
curl https://get.acme.sh | sh
source ~/.bashrc

# 重新登录以应用Docker组权限
```

### 2. 部署配置
```bash
# 创建部署目录
sudo mkdir -p /opt/matrix
sudo chown $USER:$USER /opt/matrix
cd /opt/matrix

# 复制部署包文件
cp -r /path/to/internal/* .

# 复制并编辑配置文件
cp config/deployment.env.template config/deployment.env
nano config/deployment.env
```

### 3. 配置参数
编辑 `config/deployment.env` 文件，设置以下必需参数：

```bash
# 基础域名配置
DOMAIN="your-domain.com"
SUBDOMAIN_MATRIX="matrix"
HTTPS_PORT="8443"

# Cloudflare DNS配置
CLOUDFLARE_API_TOKEN="your_api_token"
CLOUDFLARE_ZONE_ID="your_zone_id"

# 安全配置
DB_PASSWORD="$(openssl rand -base64 32)"
COTURN_SHARED_SECRET="$(openssl rand -base64 32)"

# 部署路径
DEPLOY_DIR="/opt/matrix"

# RouterOS API配置 (可选，用于更可靠的IP监控)
ROUTEROS_HOST="***********"
ROUTEROS_USER="matrix-api"
ROUTEROS_PASSWORD="your_password"
```

### 3.1. RouterOS配置 (可选)
如果您使用MikroTik RouterOS路由器，可以配置API以获得更可靠的IP监控：

```bash
# 安装RouterOS API依赖
../tools/install_routeros_deps.sh

# 验证RouterOS配置
python3 ../tools/validate_config.py --config-file config/deployment.env

# 测试RouterOS连接
../tools/test_routeros_connection.sh *********** admin password
```

### 4. 初始化部署
```bash
# 设置脚本执行权限
chmod +x scripts/*.sh

# 运行初始化脚本
./scripts/setup.sh

# 启动服务
docker compose up -d

# 检查服务状态
docker compose ps
```

### 5. 验证部署
```bash
# 检查服务健康状态
./scripts/health_check.sh

# 测试Matrix服务
curl -k https://matrix.your-domain.com:8443/_matrix/client/versions

# 检查证书状态
./scripts/certificate_manager.sh --check-only

# 验证IP同步
./scripts/ip_watchdog.sh --check-only
```

## 服务组件

### 核心服务
- **Synapse**: Matrix Homeserver核心服务
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和事件流
- **Nginx**: 反向代理和TLS终止
- **Coturn**: TURN/STUN服务器

### 自动化脚本
- **setup.sh**: 初始化部署脚本
- **ip_watchdog.sh**: IP监控和同步脚本 (支持RouterOS API)
- **certificate_manager.sh**: 证书管理脚本
- **init_certificate_links.sh**: 证书符号链接管理脚本
- **health_check.sh**: 系统健康检查脚本
- **admin.sh**: Matrix管理员工具脚本
- **routeros_setup.sh**: RouterOS API配置和测试脚本
- **backup.sh**: 数据备份脚本

## 配置文件

### 主要配置
- `config/deployment.env`: 主要环境配置
- `docker-compose.yml`: 容器编排配置
- `config/nginx.conf`: Nginx配置模板
- `config/homeserver.yaml`: Synapse配置模板
- `config/turnserver.conf`: Coturn配置模板

### 数据目录
```
data/
├── acme/           # SSL证书存储
├── nginx/          # Nginx配置和证书软链接
├── coturn/         # Coturn配置和证书软链接
├── synapse/        # Synapse数据和媒体文件
├── postgres/       # PostgreSQL数据
├── redis/          # Redis数据
└── logs/           # 系统日志
```

## 运维管理

### 日常维护
```bash
# 查看服务状态
docker compose ps

# 查看服务日志
docker compose logs [service_name]

# 重启服务
docker compose restart [service_name]

# 更新服务
docker compose pull
docker compose up -d
```

### 自动化任务
```bash
# 配置定时任务
crontab -e

# 添加以下任务
# IP监控 - 每分钟执行
* * * * * /opt/matrix/scripts/ip_watchdog.sh >> /var/log/matrix/ip_watchdog.log 2>&1

# 证书管理 - 每天凌晨2点执行
0 2 * * * /opt/matrix/scripts/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1

# 健康检查 - 每5分钟执行
*/5 * * * * /opt/matrix/scripts/health_check.sh >> /var/log/matrix/health_check.log 2>&1

# 备份 - 每天凌晨3点执行
0 3 * * * /opt/matrix/scripts/backup.sh >> /var/log/matrix/backup.log 2>&1
```

### 监控和告警
```bash
# 系统健康检查
./scripts/health_check.sh

# 详细健康检查
./scripts/health_check.sh --detailed

# 健康检查并自动修复问题
./scripts/health_check.sh --detailed --fix

# 检查证书有效期
./scripts/certificate_manager.sh --check-expiry

# 检查证书符号链接状态
./scripts/init_certificate_links.sh status

# 验证DNS解析
nslookup matrix.your-domain.com

# 测试RouterOS连接 (如果配置了)
python3 ../tools/validate_config.py --config-file config/deployment.env
```

### RouterOS管理 (如果配置了)
```bash
# 测试RouterOS API连接
python3 ../tools/routeros_client.py --host *********** --user admin --password password test

# 获取WAN接口IP
python3 ../tools/routeros_client.py --host *********** --user admin --password password get-wan-ip

# 列出网络接口
python3 ../tools/routeros_client.py --host *********** --user admin --password password list-interfaces

# 查看IP地址分配
python3 ../tools/routeros_client.py --host *********** --user admin --password password list-addresses
```

### Matrix管理
```bash
# 创建用户
./scripts/admin.sh user create alice

# 设置管理员
./scripts/admin.sh user make-admin alice

# 列出所有用户
./scripts/admin.sh user list

# 查看服务器状态
./scripts/admin.sh server status

# 查看服务器统计
./scripts/admin.sh server stats

# 列出房间
./scripts/admin.sh room list

# 数据库备份
./scripts/admin.sh db backup
```

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查Docker状态
sudo systemctl status docker

# 检查端口占用
sudo netstat -tlnp | grep :8443

# 检查配置文件
./scripts/validate_config.sh
```

#### 2. 证书问题
```bash
# 检查证书有效期
openssl x509 -in data/nginx/certs/fullchain.cer -noout -dates

# 手动续期证书
./scripts/certificate_manager.sh --force-renew

# 检查acme.sh状态
acme.sh --list
```

#### 3. IP同步问题
```bash
# 检查当前公网IP
curl -s https://ipv4.icanhazip.com/

# 检查DNS记录
nslookup matrix.your-domain.com

# 手动更新IP
./scripts/ip_watchdog.sh --force-update

# 验证IP同步状态
./scripts/health_check.sh
```

#### 4. 证书符号链接问题
```bash
# 检查证书链接状态
./scripts/init_certificate_links.sh status

# 清理无效链接
./scripts/init_certificate_links.sh cleanup

# 重新初始化链接
./scripts/init_certificate_links.sh init

# 验证修复结果
./scripts/health_check.sh --detailed
```

#### 4. 数据库连接问题
```bash
# 检查数据库状态
docker compose exec db pg_isready -U synapse

# 查看数据库日志
docker compose logs db

# 重启数据库
docker compose restart db
```

## 备份和恢复

### 备份策略
```bash
# 手动备份
./scripts/backup.sh

# 备份文件位置
ls -la backup/
```

### 恢复流程
```bash
# 停止服务
docker compose down

# 恢复数据库
docker compose exec db psql -U synapse -d synapse < backup/db_backup.sql

# 恢复配置文件
tar -xzf backup/config_backup.tar.gz

# 恢复媒体文件
rsync -av backup/media/ data/synapse/media/

# 启动服务
docker compose up -d
```

## 安全注意事项

1. **定期更新**: 保持系统和容器镜像的最新版本
2. **密码安全**: 使用强密码并定期更换
3. **访问控制**: 限制SSH访问和管理端口
4. **防火墙**: 仅开放必要的端口
5. **备份加密**: 对备份文件进行加密存储
6. **日志监控**: 定期检查系统和应用日志

## 支持和帮助

- **文档**: 查看 `docs/` 目录中的详细文档
- **日志**: 检查 `data/logs/` 目录中的日志文件
- **脚本帮助**: 运行 `./scripts/script_name.sh --help` 查看帮助信息
- **配置验证**: 使用 `./scripts/validate_config.sh` 验证配置

---

**注意**: 本部署包需要与外部指路牌服务（Signpost VPS）配合使用，请确保已正确配置外部服务。
