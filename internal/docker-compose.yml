version: '3.8'

services:
  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: matrix_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-synapse}
      POSTGRES_USER: ${DB_USER:-synapse}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - matrix_network
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-1g}
          cpus: '${POSTGRES_CPU_LIMIT:-1}'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-synapse}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: matrix_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - ./data/redis:/data
    networks:
      - matrix_network
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-512m}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Synapse Matrix服务器
  synapse:
    image: matrixdotorg/synapse:latest
    container_name: matrix_synapse
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      SYNAPSE_SERVER_NAME: ${DOMAIN}
      SYNAPSE_REPORT_STATS: "no"
      SYNAPSE_CONFIG_PATH: /data/homeserver.yaml
    volumes:
      - ./data/synapse:/data
      - ./config/homeserver.yaml:/data/homeserver.yaml:ro
      - ./config/log.config:/data/log.config:ro
    networks:
      - matrix_network
    deploy:
      resources:
        limits:
          memory: ${SYNAPSE_MEMORY_LIMIT:-2g}
          cpus: '${SYNAPSE_CPU_LIMIT:-2}'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Coturn TURN/STUN服务器
  coturn:
    image: coturn/coturn:latest
    container_name: matrix_coturn
    restart: unless-stopped
    network_mode: host
    volumes:
      - ./data/coturn/conf/turnserver.conf:/etc/coturn/turnserver.conf:ro
      - ./data/coturn/certs:/etc/coturn/certs:ro
      - ./data/coturn/logs:/var/log/coturn
      # 挂载acme.sh目录以访问原始证书 (只读)
      - /root/.acme.sh:/root/.acme.sh:ro
    deploy:
      resources:
        limits:
          memory: ${COTURN_MEMORY_LIMIT:-512m}
          cpus: '${COTURN_CPU_LIMIT:-1}'

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: matrix_nginx
    restart: unless-stopped
    depends_on:
      - synapse
    ports:
      - "127.0.0.1:${HTTPS_PORT:-8443}:${HTTPS_PORT:-8443}"
    volumes:
      - ./data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./data/nginx/conf/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro
      - ./data/nginx/certs:/etc/nginx/certs:ro
      - ./data/nginx/logs:/var/log/nginx
      - ./data/synapse/media:/var/www/matrix/media:ro
      # 挂载acme.sh目录以访问原始证书 (只读)
      - /root/.acme.sh:/root/.acme.sh:ro
    networks:
      - matrix_network
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-256m}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  matrix_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  synapse_data:
    driver: local
  nginx_logs:
    driver: local
  coturn_logs:
    driver: local
