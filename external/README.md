# Matrix Homeserver 外部指路牌服务部署包

## 概述

本部署包包含Matrix Homeserver的外部指路牌服务（Signpost VPS），运行在具有静态公网IP的VPS上。主要功能是提供Matrix联邦发现所需的`.well-known`文件，引导客户端和联邦服务器找到实际的Matrix服务。

## 系统要求

### 硬件要求
- **CPU**: 1核心
- **内存**: 1GB
- **存储**: 20GB SSD
- **网络**: 静态公网IP，标准80/443端口

### 软件要求
- **操作系统**: Ubuntu 20.04 LTS或更新版本
- **Nginx**: 1.18或更新版本
- **Certbot**: 最新版本

### 网络要求
- **域名**: 已配置的主域名
- **DNS**: 主域名A记录指向此VPS的静态IP
- **端口**: 80和443端口可用

## 快速部署

### 1. 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Nginx
sudo apt install nginx -y

# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 启动并启用Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 部署配置
```bash
# 创建部署目录
sudo mkdir -p /opt/matrix-signpost
cd /opt/matrix-signpost

# 复制部署包文件
cp -r /path/to/external/* .

# 复制并编辑配置文件
cp config/signpost.env.template config/signpost.env
sudo nano config/signpost.env
```

### 3. 配置参数
编辑 `config/signpost.env` 文件，设置以下参数：

```bash
# 基础域名配置
DOMAIN="your-domain.com"
SUBDOMAIN_MATRIX="matrix"
HTTPS_PORT="8443"

# 管理员邮箱
ADMIN_EMAIL="<EMAIL>"

# 部署路径
DEPLOY_DIR="/opt/matrix-signpost"
```

### 4. 执行部署
```bash
# 设置脚本执行权限
chmod +x scripts/*.sh

# 运行部署脚本
sudo ./scripts/deploy.sh

# 检查服务状态
sudo systemctl status nginx
```

### 5. 验证部署
```bash
# 测试HTTP重定向
curl -I http://your-domain.com

# 测试HTTPS访问
curl -I https://your-domain.com

# 测试.well-known文件
curl https://your-domain.com/.well-known/matrix/client
curl https://your-domain.com/.well-known/matrix/server
```

## 服务组件

### 核心服务
- **Nginx**: Web服务器，提供静态文件服务
- **Certbot**: SSL证书自动管理

### 配置文件
- **nginx.conf**: Nginx主配置文件
- **signpost.conf**: 站点配置文件
- **.well-known文件**: Matrix联邦发现文件

### 自动化脚本
- **deploy.sh**: 部署脚本
- **update_wellknown.sh**: 更新.well-known文件脚本
- **renew_cert.sh**: 证书续期脚本

## 配置文件

### 主要配置
- `config/signpost.env`: 主要环境配置
- `config/nginx.conf`: Nginx配置模板
- `config/signpost.conf`: 站点配置模板
- `config/well-known/`: .well-known文件模板

### 目录结构
```
/opt/matrix-signpost/
├── config/                 # 配置文件
├── scripts/                # 部署脚本
├── www/                    # Web根目录
│   └── .well-known/        # Matrix发现文件
└── logs/                   # 日志文件
```

## 运维管理

### 日常维护
```bash
# 查看Nginx状态
sudo systemctl status nginx

# 重载Nginx配置
sudo nginx -t && sudo systemctl reload nginx

# 查看访问日志
sudo tail -f /var/log/nginx/access.log

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 证书管理
```bash
# 手动续期证书
sudo certbot renew

# 测试证书续期
sudo certbot renew --dry-run

# 查看证书状态
sudo certbot certificates
```

### 更新.well-known文件
```bash
# 更新Matrix服务配置
sudo ./scripts/update_wellknown.sh

# 验证更新
curl https://your-domain.com/.well-known/matrix/client
```

## 监控和告警

### 服务监控
```bash
# 检查Nginx进程
ps aux | grep nginx

# 检查端口监听
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 日志监控
```bash
# 监控访问日志
sudo tail -f /var/log/nginx/access.log | grep -E "(well-known|matrix)"

# 监控错误日志
sudo tail -f /var/log/nginx/error.log

# 检查SSL证书到期时间
echo | openssl s_client -servername your-domain.com -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

## 故障排除

### 常见问题

#### 1. Nginx无法启动
```bash
# 检查配置文件语法
sudo nginx -t

# 检查端口占用
sudo netstat -tlnp | grep :80

# 查看详细错误信息
sudo systemctl status nginx -l
```

#### 2. SSL证书问题
```bash
# 检查证书文件
sudo ls -la /etc/letsencrypt/live/your-domain.com/

# 重新申请证书
sudo certbot --nginx -d your-domain.com

# 检查证书有效期
sudo certbot certificates
```

#### 3. .well-known文件无法访问
```bash
# 检查文件权限
sudo ls -la /var/www/html/.well-known/

# 检查Nginx配置
sudo nginx -t

# 测试本地访问
curl -I localhost/.well-known/matrix/client
```

#### 4. DNS解析问题
```bash
# 检查域名解析
nslookup your-domain.com

# 检查DNS传播
dig your-domain.com @*******

# 测试从外部访问
curl -I https://your-domain.com
```

## 安全配置

### 防火墙设置
```bash
# 允许HTTP和HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 允许SSH (根据需要)
sudo ufw allow 22/tcp

# 启用防火墙
sudo ufw enable

# 查看防火墙状态
sudo ufw status
```

### 安全加固
```bash
# 隐藏Nginx版本信息
echo "server_tokens off;" | sudo tee -a /etc/nginx/nginx.conf

# 设置安全头
# (已在配置文件中包含)

# 定期更新系统
sudo apt update && sudo apt upgrade -y

# 监控登录日志
sudo tail -f /var/log/auth.log
```

## 备份和恢复

### 备份策略
```bash
# 备份配置文件
sudo tar -czf /backup/nginx-config-$(date +%Y%m%d).tar.gz /etc/nginx/

# 备份.well-known文件
sudo tar -czf /backup/wellknown-$(date +%Y%m%d).tar.gz /var/www/html/.well-known/

# 备份SSL证书
sudo tar -czf /backup/ssl-certs-$(date +%Y%m%d).tar.gz /etc/letsencrypt/
```

### 恢复流程
```bash
# 恢复配置文件
sudo tar -xzf /backup/nginx-config-YYYYMMDD.tar.gz -C /

# 恢复.well-known文件
sudo tar -xzf /backup/wellknown-YYYYMMDD.tar.gz -C /

# 恢复SSL证书
sudo tar -xzf /backup/ssl-certs-YYYYMMDD.tar.gz -C /

# 重启Nginx
sudo systemctl restart nginx
```

## 性能优化

### Nginx优化
```bash
# 调整worker进程数
# worker_processes auto;

# 启用gzip压缩
# gzip on;

# 设置缓存头
# expires 1h;

# 限制请求频率
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
```

### 系统优化
```bash
# 调整文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化TCP参数
echo "net.core.somaxconn = 65536" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 支持和帮助

- **文档**: 查看 `docs/` 目录中的详细文档
- **日志**: 检查 `/var/log/nginx/` 目录中的日志文件
- **脚本帮助**: 运行 `./scripts/script_name.sh --help` 查看帮助信息
- **配置验证**: 使用 `sudo nginx -t` 验证配置

---

**注意**: 本部署包需要与内部核心服务（LAN Server）配合使用，请确保已正确配置内部服务并更新.well-known文件中的服务地址。
