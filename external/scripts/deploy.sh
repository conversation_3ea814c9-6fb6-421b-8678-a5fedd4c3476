#!/bin/bash

# ================================================================
# Matrix Homeserver 外部指路牌部署脚本
# ================================================================
# 功能: 自动化部署外部指路牌服务
# 作者: Matrix Homeserver部署项目
# 版本: 1.0.0
# ================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 默认配置
LOG_FILE="/var/log/matrix-signpost-deploy.log"
FORCE_DEPLOY=false
SKIP_CERT=false
SKIP_NGINX=false

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $*" | tee -a "$LOG_FILE"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            --skip-cert)
                SKIP_CERT=true
                shift
                ;;
            --skip-nginx)
                SKIP_NGINX=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 外部指路牌部署脚本

用法: $0 [选项]

选项:
    --force         强制重新部署，覆盖现有配置
    --skip-cert     跳过SSL证书申请
    --skip-nginx    跳过Nginx配置
    --help          显示此帮助信息

示例:
    $0                    # 完整部署
    $0 --force            # 强制重新部署
    $0 --skip-cert        # 跳过证书申请

EOF
}

# 检查运行权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "不支持的操作系统"
        return 1
    fi
    
    local os_info
    os_info=$(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)
    log_info "操作系统: $os_info"
    
    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_error "网络连接检查失败"
        return 1
    fi
    
    log_info "系统环境检查完成"
    return 0
}

# 检查和安装依赖
install_dependencies() {
    log_info "检查和安装依赖"
    
    # 更新包列表
    apt update
    
    # 安装必需的包
    local packages=(
        "nginx"
        "certbot"
        "python3-certbot-nginx"
        "curl"
        "wget"
        "ufw"
    )
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_info "安装 $package"
            apt install -y "$package"
        else
            log_debug "$package 已安装"
        fi
    done
    
    log_info "依赖安装完成"
}

# 加载配置文件
load_configuration() {
    local config_file="${PROJECT_DIR}/config/signpost.env"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "请复制模板文件并编辑配置:"
        log_info "  cp config/signpost.env.template config/signpost.env"
        log_info "  nano config/signpost.env"
        return 1
    fi
    
    source "$config_file"
    
    # 验证必需配置
    local required_vars=(
        "DOMAIN"
        "SUBDOMAIN_MATRIX"
        "HTTPS_PORT"
        "ADMIN_EMAIL"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "必需配置项未设置: $var"
            return 1
        fi
    done
    
    log_info "配置文件加载完成"
    return 0
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙"
    
    # 启用UFW
    ufw --force enable
    
    # 允许SSH
    ufw allow ssh
    
    # 允许HTTP和HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # 显示防火墙状态
    ufw status
    
    log_info "防火墙配置完成"
}

# 配置Nginx
configure_nginx() {
    if [[ "$SKIP_NGINX" == "true" ]]; then
        log_info "跳过Nginx配置"
        return 0
    fi
    
    log_info "配置Nginx"
    
    # 备份原始配置
    if [[ -f /etc/nginx/nginx.conf ]] && [[ "$FORCE_DEPLOY" != "true" ]]; then
        cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 生成主配置文件
    envsubst < "${PROJECT_DIR}/config/nginx.conf.template" > /etc/nginx/nginx.conf
    
    # 生成站点配置文件
    envsubst < "${PROJECT_DIR}/config/signpost.conf.template" > /etc/nginx/sites-available/signpost
    
    # 禁用默认站点
    if [[ -L /etc/nginx/sites-enabled/default ]]; then
        rm /etc/nginx/sites-enabled/default
    fi
    
    # 启用新站点
    ln -sf /etc/nginx/sites-available/signpost /etc/nginx/sites-enabled/
    
    # 测试配置
    if nginx -t; then
        log_info "Nginx配置验证成功"
    else
        log_error "Nginx配置验证失败"
        return 1
    fi
    
    # 启动并启用Nginx
    systemctl enable nginx
    systemctl start nginx
    
    log_info "Nginx配置完成"
    return 0
}

# 创建Web目录结构
create_web_structure() {
    log_info "创建Web目录结构"
    
    # 创建Web根目录
    mkdir -p "${WEB_ROOT}"
    mkdir -p "${WEB_ROOT}/.well-known/matrix"
    
    # 设置权限
    chown -R www-data:www-data "${WEB_ROOT}"
    chmod -R 755 "${WEB_ROOT}"
    
    log_info "Web目录结构创建完成"
}

# 申请SSL证书
obtain_ssl_certificate() {
    if [[ "$SKIP_CERT" == "true" ]]; then
        log_info "跳过SSL证书申请"
        return 0
    fi
    
    log_info "申请SSL证书"
    
    # 检查证书是否已存在
    if [[ -f "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" ]] && [[ "$FORCE_DEPLOY" != "true" ]]; then
        log_info "SSL证书已存在，跳过申请"
        return 0
    fi
    
    # 停止Nginx以释放80端口
    systemctl stop nginx
    
    # 申请证书
    if certbot certonly --standalone \
        --email "${ADMIN_EMAIL}" \
        --agree-tos \
        --no-eff-email \
        -d "${DOMAIN}"; then
        log_info "SSL证书申请成功"
    else
        log_error "SSL证书申请失败"
        systemctl start nginx
        return 1
    fi
    
    # 重新启动Nginx
    systemctl start nginx
    
    # 设置自动续期
    setup_cert_renewal
    
    log_info "SSL证书配置完成"
    return 0
}

# 设置证书自动续期
setup_cert_renewal() {
    log_info "设置证书自动续期"
    
    # 创建续期脚本
    cat > /etc/cron.d/certbot-renew << EOF
# 每天检查证书续期
0 2 * * * root certbot renew --quiet --nginx && systemctl reload nginx
EOF
    
    # 测试续期
    certbot renew --dry-run
    
    log_info "证书自动续期设置完成"
}

# 创建.well-known文件
create_wellknown_files() {
    log_info "创建.well-known文件"
    
    local wellknown_dir="${WEB_ROOT}/.well-known/matrix"
    
    # 创建客户端配置文件
    cat > "${wellknown_dir}/client" << EOF
{
    "m.homeserver": {
        "base_url": "https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}"
    },
    "io.element.e2ee": {
        "default": true
    },
    "io.element.integrations": {
        "url": "${INTEGRATION_MANAGER_URL:-https://scalar.vector.im/}"
    }
}
EOF
    
    # 创建服务器配置文件
    cat > "${wellknown_dir}/server" << EOF
{
    "m.server": "${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}"
}
EOF
    
    # 设置权限
    chown -R www-data:www-data "${wellknown_dir}"
    chmod -R 644 "${wellknown_dir}"/*
    
    log_info ".well-known文件创建完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署"
    
    # 等待服务启动
    sleep 5
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        log_info "Nginx服务运行正常"
    else
        log_error "Nginx服务未运行"
        return 1
    fi
    
    # 测试HTTP重定向
    local http_status
    http_status=$(curl -s -o /dev/null -w "%{http_code}" "http://${DOMAIN}" || echo "000")
    if [[ "$http_status" == "301" ]]; then
        log_info "HTTP重定向测试通过"
    else
        log_warn "HTTP重定向测试失败 (状态码: $http_status)"
    fi
    
    # 测试HTTPS访问
    local https_status
    https_status=$(curl -s -o /dev/null -w "%{http_code}" "https://${DOMAIN}" || echo "000")
    if [[ "$https_status" == "200" ]]; then
        log_info "HTTPS访问测试通过"
    else
        log_warn "HTTPS访问测试失败 (状态码: $https_status)"
    fi
    
    # 测试.well-known文件
    local client_status server_status
    client_status=$(curl -s -o /dev/null -w "%{http_code}" "https://${DOMAIN}/.well-known/matrix/client" || echo "000")
    server_status=$(curl -s -o /dev/null -w "%{http_code}" "https://${DOMAIN}/.well-known/matrix/server" || echo "000")
    
    if [[ "$client_status" == "200" ]] && [[ "$server_status" == "200" ]]; then
        log_info ".well-known文件测试通过"
    else
        log_warn ".well-known文件测试失败 (client: $client_status, server: $server_status)"
    fi
    
    log_info "部署验证完成"
    return 0
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    
    cat << EOF

=================================================================
Matrix Homeserver 外部指路牌部署完成
=================================================================

服务信息:
  域名: ${DOMAIN}
  Matrix服务器: ${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}
  管理员邮箱: ${ADMIN_EMAIL}

访问地址:
  主页: https://${DOMAIN}
  健康检查: https://${DOMAIN}/health
  状态页面: https://${DOMAIN}/status

.well-known文件:
  客户端: https://${DOMAIN}/.well-known/matrix/client
  服务器: https://${DOMAIN}/.well-known/matrix/server

服务状态:
$(systemctl status nginx --no-pager -l)

SSL证书信息:
$(certbot certificates 2>/dev/null || echo "证书信息获取失败")

下一步操作:
1. 验证.well-known文件:
   curl https://${DOMAIN}/.well-known/matrix/client
   curl https://${DOMAIN}/.well-known/matrix/server

2. 检查服务状态:
   sudo systemctl status nginx

3. 查看访问日志:
   sudo tail -f /var/log/nginx/access.log

4. 更新.well-known文件 (如果需要):
   sudo ./scripts/update_wellknown.sh

=================================================================

EOF
}

# 主要处理逻辑
main() {
    log_info "开始Matrix Homeserver外部指路牌部署"
    
    # 检查运行权限
    check_permissions
    
    # 检查系统环境
    if ! check_environment; then
        log_error "系统环境检查失败"
        exit 1
    fi
    
    # 安装依赖
    install_dependencies
    
    # 加载配置
    if ! load_configuration; then
        log_error "配置加载失败"
        exit 1
    fi
    
    # 配置防火墙
    configure_firewall
    
    # 创建Web目录结构
    create_web_structure
    
    # 配置Nginx
    if ! configure_nginx; then
        log_error "Nginx配置失败"
        exit 1
    fi
    
    # 申请SSL证书
    if ! obtain_ssl_certificate; then
        log_error "SSL证书申请失败"
        exit 1
    fi
    
    # 创建.well-known文件
    create_wellknown_files
    
    # 重新加载Nginx配置
    systemctl reload nginx
    
    # 验证部署
    if ! verify_deployment; then
        log_warn "部署验证失败，但继续执行"
    fi
    
    # 显示部署信息
    show_deployment_info
    
    log_info "Matrix Homeserver外部指路牌部署完成"
}

# 解析参数并执行
parse_arguments "$@"

# 创建日志文件
touch "$LOG_FILE"
chmod 644 "$LOG_FILE"

# 执行主逻辑
main
