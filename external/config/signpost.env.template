# ================================================================
# Matrix Homeserver 外部指路牌服务配置
# ================================================================
# 复制此文件为 signpost.env 并根据实际环境修改配置值
# 注意：请勿将包含敏感信息的 signpost.env 文件提交到版本控制系统

# ================================================================
# 基础域名配置
# ================================================================

# 主域名 (此VPS托管的域名)
DOMAIN="example.com"

# Matrix服务子域名 (指向内部服务器)
SUBDOMAIN_MATRIX="matrix"

# 完整的Matrix服务域名 (自动生成，无需修改)
MATRIX_DOMAIN="${SUBDOMAIN_MATRIX}.${DOMAIN}"

# ================================================================
# 网络端口配置
# ================================================================

# 内部服务器的HTTPS端口 (由于ISP封禁443，使用自定义端口)
HTTPS_PORT="8443"

# 本VPS的HTTP/HTTPS端口 (标准端口)
HTTP_PORT="80"
HTTPS_VPS_PORT="443"

# ================================================================
# 部署路径配置
# ================================================================

# 主部署目录
DEPLOY_DIR="/opt/matrix-signpost"

# Web根目录
WEB_ROOT="/var/www/html"

# Nginx配置目录
NGINX_CONF_DIR="/etc/nginx"

# 日志目录
LOG_DIR="/var/log/nginx"

# ================================================================
# SSL证书配置
# ================================================================

# 管理员邮箱 (用于Let's Encrypt证书申请)
ADMIN_EMAIL="admin@${DOMAIN}"

# 证书存储路径
CERT_PATH="/etc/letsencrypt/live/${DOMAIN}"

# 证书自动续期
AUTO_RENEW="true"

# ================================================================
# Matrix服务配置
# ================================================================

# Matrix客户端配置
MATRIX_CLIENT_BASE_URL="https://${MATRIX_DOMAIN}:${HTTPS_PORT}"

# Matrix服务器配置
MATRIX_SERVER="https://${MATRIX_DOMAIN}:${HTTPS_PORT}"

# Element Web客户端URL (可选)
ELEMENT_WEB_URL="https://app.element.io"

# 集成管理器URL (可选)
INTEGRATION_MANAGER_URL="https://scalar.vector.im"

# ================================================================
# 安全配置
# ================================================================

# 安全头配置
ENABLE_HSTS="true"
HSTS_MAX_AGE="31536000"

# 内容安全策略
ENABLE_CSP="true"

# X-Frame-Options
X_FRAME_OPTIONS="DENY"

# X-Content-Type-Options
X_CONTENT_TYPE_OPTIONS="nosniff"

# ================================================================
# 性能配置
# ================================================================

# Gzip压缩
ENABLE_GZIP="true"

# 缓存配置
CACHE_CONTROL_MAX_AGE="3600"

# 访问日志
ENABLE_ACCESS_LOG="true"

# 错误日志级别
ERROR_LOG_LEVEL="warn"

# ================================================================
# 监控配置
# ================================================================

# 健康检查端点
ENABLE_HEALTH_CHECK="true"
HEALTH_CHECK_PATH="/health"

# 状态页面
ENABLE_STATUS_PAGE="true"
STATUS_PAGE_PATH="/status"

# ================================================================
# 备份配置
# ================================================================

# 备份目录
BACKUP_DIR="/backup"

# 备份保留天数
BACKUP_RETENTION_DAYS="30"

# 自动备份
AUTO_BACKUP="true"

# ================================================================
# 环境标识
# ================================================================

# 部署环境
ENVIRONMENT="production"

# 服务器角色
SERVER_ROLE="signpost"

# 部署版本
DEPLOYMENT_VERSION="1.0.0"

# 最后更新时间
LAST_UPDATED="$(date -u +%Y-%m-%dT%H:%M:%SZ)"

# ================================================================
# 配置验证
# ================================================================

# 必需配置项检查
REQUIRED_VARS=(
    "DOMAIN"
    "SUBDOMAIN_MATRIX"
    "HTTPS_PORT"
    "ADMIN_EMAIL"
)

# ================================================================
# 使用说明
# ================================================================

# 1. 复制此模板文件：
#    cp config/signpost.env.template config/signpost.env
#
# 2. 编辑配置文件：
#    nano config/signpost.env
#
# 3. 设置正确的文件权限：
#    chmod 600 config/signpost.env
#
# 4. 验证配置：
#    ./scripts/validate_config.sh
#
# 5. 开始部署：
#    sudo ./scripts/deploy.sh
