# Matrix Homeserver 外部指路牌站点配置
# 此文件由脚本自动生成，请勿手动编辑

# HTTPS服务器配置
server {
    listen ${HTTPS_VPS_PORT:-443} ssl http2;
    listen [::]:${HTTPS_VPS_PORT:-443} ssl http2;
    server_name ${DOMAIN};

    # SSL证书配置
    ssl_certificate ${CERT_PATH}/fullchain.pem;
    ssl_certificate_key ${CERT_PATH}/privkey.pem;
    ssl_trusted_certificate ${CERT_PATH}/chain.pem;

    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=${HSTS_MAX_AGE:-31536000}; includeSubDomains" always;

    # 安全头
    add_header X-Frame-Options ${X_FRAME_OPTIONS:-DENY} always;
    add_header X-Content-Type-Options ${X_CONTENT_TYPE_OPTIONS:-nosniff} always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 内容安全策略 (如果启用)
    # add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;

    # Web根目录
    root ${WEB_ROOT};
    index index.html index.htm;

    # Matrix .well-known 文件
    location /.well-known/matrix/ {
        # 速率限制
        limit_req zone=wellknown burst=10 nodelay;
        
        # CORS头
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        
        # 缓存控制
        add_header Cache-Control "public, max-age=${CACHE_CONTROL_MAX_AGE:-3600}" always;
        
        # 内容类型
        location ~ \.(json)$ {
            add_header Content-Type "application/json" always;
        }
        
        # 尝试文件，如果不存在返回404
        try_files $uri $uri/ =404;
    }

    # Matrix客户端.well-known文件
    location = /.well-known/matrix/client {
        limit_req zone=wellknown burst=10 nodelay;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Content-Type "application/json" always;
        add_header Cache-Control "public, max-age=${CACHE_CONTROL_MAX_AGE:-3600}" always;
        
        return 200 '{"m.homeserver":{"base_url":"${MATRIX_CLIENT_BASE_URL}"},"io.element.e2ee":{"default":true},"io.element.integrations":{"url":"${INTEGRATION_MANAGER_URL:-https://scalar.vector.im/"}"}}';
    }

    # Matrix服务器.well-known文件
    location = /.well-known/matrix/server {
        limit_req zone=wellknown burst=10 nodelay;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Content-Type "application/json" always;
        add_header Cache-Control "public, max-age=${CACHE_CONTROL_MAX_AGE:-3600}" always;
        
        return 200 '{"m.server":"${MATRIX_DOMAIN}:${HTTPS_PORT}"}';
    }

    # 健康检查端点
    location ${HEALTH_CHECK_PATH:-/health} {
        access_log off;
        add_header Content-Type "application/json" always;
        return 200 '{"status":"ok","service":"matrix-signpost","timestamp":"$time_iso8601"}';
    }

    # 状态页面
    location ${STATUS_PAGE_PATH:-/status} {
        access_log off;
        add_header Content-Type "application/json" always;
        return 200 '{"service":"Matrix Homeserver Signpost","domain":"${DOMAIN}","matrix_server":"${MATRIX_DOMAIN}:${HTTPS_PORT}","version":"${DEPLOYMENT_VERSION:-1.0.0}","environment":"${ENVIRONMENT:-production}"}';
    }

    # 机器人和爬虫文件
    location = /robots.txt {
        access_log off;
        add_header Content-Type "text/plain" always;
        return 200 "User-agent: *\nDisallow: /\n";
    }

    # 网站图标
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        return 404;
    }

    # 默认页面
    location / {
        # 简单的信息页面
        add_header Content-Type "text/html" always;
        return 200 '<!DOCTYPE html>
<html>
<head>
    <title>Matrix Homeserver</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .info { background: #e8f4fd; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .matrix-id { font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; }
        a { color: #0066cc; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Matrix Homeserver</h1>
        <div class="info">
            <p>这是一个Matrix Homeserver的联邦发现服务。</p>
            <p>Matrix服务器地址: <strong>${MATRIX_DOMAIN}:${HTTPS_PORT}</strong></p>
            <p>要连接到此服务器，请使用以下格式的Matrix ID:</p>
            <div class="matrix-id">@username:${DOMAIN}</div>
        </div>
        <p>推荐的Matrix客户端:</p>
        <ul>
            <li><a href="https://app.element.io" target="_blank">Element (Web)</a></li>
            <li><a href="https://element.io/get-started" target="_blank">Element (移动端)</a></li>
            <li><a href="https://nheko.im/" target="_blank">Nheko (桌面端)</a></li>
        </ul>
    </div>
</body>
</html>';
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        add_header Content-Type "text/html" always;
        return 404 '<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>404 Not Found</h1><p>The requested resource was not found on this server.</p></body></html>';
    }
    
    location = /50x.html {
        internal;
        add_header Content-Type "text/html" always;
        return 500 '<!DOCTYPE html><html><head><title>Server Error</title></head><body><h1>Server Error</h1><p>The server encountered an internal error.</p></body></html>';
    }
}

# HTTP服务器配置 (重定向到HTTPS)
server {
    listen ${HTTP_PORT:-80};
    listen [::]:${HTTP_PORT:-80};
    server_name ${DOMAIN};

    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root ${WEB_ROOT};
        try_files $uri =404;
    }

    # 重定向所有其他请求到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# 默认服务器配置 (拒绝未知域名)
server {
    listen ${HTTP_PORT:-80} default_server;
    listen [::]:${HTTP_PORT:-80} default_server;
    listen ${HTTPS_VPS_PORT:-443} ssl default_server;
    listen [::]:${HTTPS_VPS_PORT:-443} ssl default_server;

    # 使用自签名证书或默认证书
    ssl_certificate /etc/ssl/certs/ssl-cert-snakeoil.pem;
    ssl_certificate_key /etc/ssl/private/ssl-cert-snakeoil.key;

    # 拒绝所有请求
    return 444;
}
